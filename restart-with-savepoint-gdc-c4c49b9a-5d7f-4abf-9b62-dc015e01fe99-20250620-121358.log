[2025-06-20 12:13:58] 🌐 Port-forwarding Flink JobManager REST API...
[2025-06-20 12:14:01] 🔍 Fetching running job ID...
[2025-06-20 12:14:01] ✅ Found running job: 2aaec97728c3abd2f7fa7cea4ecfc873
[2025-06-20 12:14:02] 🛑 Stopping job with drain=true and savepoint path: abfss://<EMAIL>/gdc-c4c49b9a-5d7f-4abf-9b62-dc015e01fe99/savepoints/savepoint-1750344888/savepoint-1750414438
[2025-06-20 12:14:02] 🚦 Stop triggered, trigger ID: 047976c24b305409da7ffe7562dc28f2
[2025-06-20 12:14:02] ✅ Savepoint created at: null
[2025-06-20 12:14:02] ⏳ Waiting for job to stop...
[2025-06-20 12:14:07] ✅ Job stopped with state: FINISHED
[2025-06-20 12:14:07] 📦 Patching FlinkDeployment with savepoint, savepointRedeployNonce: 1750414447 and initialSavepointPath: null
[2025-06-20 12:14:08] 🚀 Restart requested from savepoint: abfss://<EMAIL>/gdc-c4c49b9a-5d7f-4abf-9b62-dc015e01fe99/savepoints/savepoint-1750344888/savepoint-1750414438 (savepointRedeployNonce: 1750414447) and initialSavepointPath: null
[2025-06-20 12:14:08] ⏳ Verifying FlinkDeployment restart...
[2025-06-20 12:14:09] ⏳ Current job state: FINISHED (Timeout in 299s)
[2025-06-20 12:14:20] ⏳ Current job state: RECONCILING (Timeout in 288s)
[2025-06-20 12:14:31] ⏳ Current job state: RECONCILING (Timeout in 277s)
[2025-06-20 12:14:42] ⏳ Current job state: RECONCILING (Timeout in 266s)
[2025-06-20 12:14:53] ❌ FlinkDeployment restart failed - job is in FAILED state
[2025-06-20 12:14:54] === Restart Operation Summary ===
[2025-06-20 12:14:54] FlinkDeployment: gdc-c4c49b9a-5d7f-4abf-9b62-dc015e01fe99
[2025-06-20 12:14:54] Namespace: str-int
[2025-06-20 12:14:54] Savepoint path: null
[2025-06-20 12:14:54] Final state: FAILED
[2025-06-20 12:14:54] ❌ Restart operation did not complete successfully within the timeout period
[2025-06-20 12:14:54] 🧹 Cleaning up port-forward
