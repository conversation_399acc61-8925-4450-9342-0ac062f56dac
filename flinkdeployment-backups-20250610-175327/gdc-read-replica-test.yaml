apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  annotations:
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"flink.apache.org/v1beta1","kind":"FlinkDeployment","metadata":{"annotations":{},"labels":{"argocd.argoproj.io/instance":"str-int_int-flink-playground"},"name":"gdc-read-replica-test","namespace":"str-int"},"spec":{"flinkConfiguration":{"_ude.pipeline.dataOrderId":"bb3082d0-bd06-49a0-a83f-39223f75706b","_ude.pipeline.name":"StreamingPipeline.*********.Automation","_ude.pipeline.projectId":"0c6a9ec1-e362-4e56-9ad3-47ff54d89d5c","_ude.pipeline.revision":"1","kubernetes.container.image.pull-secrets":"mega-container-registry-secret","*********************************":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","kubernetes.taskmanager.annotations":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","state.checkpoints.dir":"UDE_REMOVED","state.savepoints.dir":"UDE_REMOVED"},"flinkVersion":"v1_20","image":"crmegahubwesteurope.azurecr.io/str-int/gdc-job:main","imagePullPolicy":"Always","ingress":{"annotations":{"nginx.ingress.kubernetes.io/auth-realm":"Authentication Required - Flink Playground","nginx.ingress.kubernetes.io/auth-secret":"deployment-service-flink-basic-auth","nginx.ingress.kubernetes.io/auth-type":"basic","nginx.ingress.kubernetes.io/rewrite-target":"/$2"},"className":"nginx","template":"str-int.apps.mega.cariad.cloud/{{name}}(/|$)(.*)"},"job":{"args":[],"entryClass":"org.ude.GdcTransformationJob","jarURI":"local:///opt/flink/lib/flink-job-all.jar","parallelism":1,"state":"running","upgradeMode":"savepoint"},"jobManager":{"replicas":1,"resource":{"cpu":1,"memory":"1Gi"}},"logConfiguration":{"logback-console.xml":"\u003cconfiguration\u003e\n  \u003cproperty name=\"pattern\" value=\"%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread) %cyan(%-12.36logger{36}) %msg%n\" /\u003e\n  \u003cappender name=\"STDOUT\" class=\"ch.qos.logback.core.ConsoleAppender\"\u003e\n      \u003ctarget\u003eSystem.out\u003c/target\u003e\n      \u003cimmediateFlush\u003etrue\u003c/immediateFlush\u003e\n      \u003cfilter class=\"ch.qos.logback.classic.filter.LevelFilter\"\u003e\n          \u003clevel\u003eERROR\u003c/level\u003e\n          \u003conMatch\u003eDENY\u003c/onMatch\u003e\n      \u003c/filter\u003e\n      \u003cfilter class=\"ch.qos.logback.classic.filter.LevelFilter\"\u003e\n          \u003clevel\u003eWARN\u003c/level\u003e\n          \u003conMatch\u003eDENY\u003c/onMatch\u003e\n      \u003c/filter\u003e\n      \u003cencoder\u003e\n          \u003cpattern\u003e${pattern}\u003c/pattern\u003e\n      \u003c/encoder\u003e\n  \u003c/appender\u003e\n\n  \u003cappender name=\"STDERR\" class=\"ch.qos.logback.core.ConsoleAppender\"\u003e\n      \u003ctarget\u003eSystem.err\u003c/target\u003e\n      \u003cimmediateFlush\u003etrue\u003c/immediateFlush\u003e\n      \u003cfilter class=\"ch.qos.logback.classic.filter.ThresholdFilter\"\u003e\n          \u003clevel\u003eWARN\u003c/level\u003e\n      \u003c/filter\u003e\n      \u003cencoder\u003e\n          \u003cpattern\u003e${pattern}\u003c/pattern\u003e\n      \u003c/encoder\u003e\n  \u003c/appender\u003e\n\n  \u003croot level=\"INFO\"\u003e\n      \u003cappender-ref ref=\"STDOUT\" /\u003e\n      \u003cappender-ref ref=\"STDERR\" /\u003e\n  \u003c/root\u003e\n\n  \u003c!-- Specific loggers --\u003e\n  \u003clogger name=\"akka\" level=\"INFO\"/\u003e\n  \u003clogger name=\"org.apache.kafka\" level=\"INFO\"/\u003e\n  \u003clogger name=\"org.apache.hadoop\" level=\"INFO\"/\u003e\n  \u003clogger name=\"org.apache.zookeeper\" level=\"INFO\"/\u003e\n  \u003clogger name=\"org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline\" level=\"OFF\"/\u003e\n\u003c/configuration\u003e\n"},"mode":"native","podTemplate":{"metadata":{"labels":{"app.kubernetes.io/name":"gdc-bb3082d0-bd06-49a0-a83f-39223f75706b","dataOrderId":"bb3082d0-bd06-49a0-a83f-39223f75706b","jobType":"gdc-job","pipelineName":"StreamingPipeline.*********.Automation","projectId":"0c6a9ec1-e362-4e56-9ad3-47ff54d89d5c"}},"spec":{"containers":[{"env":[{"name":"SCHEMA_REGISTRY_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_schema_registry_key","name":"deployment-service-gdc-job-schema-registry-secret"}}},{"name":"SCHEMA_REGISTRY_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_schema_registry_secret","name":"deployment-service-gdc-job-schema-registry-secret"}}},{"name":"KAFKA_CLUSTER_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_secret","name":"deployment-service-gdc-job-kafka-credentials"}}},{"name":"KAFKA_CLUSTER_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_key","name":"deployment-service-gdc-job-kafka-credentials"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"postgresql_admin_password","name":"deployment-service-db-password"}}},{"name":"AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-store-and-explore-upload-spn-secret"}}},{"name":"AZURE_DOWNLOAD_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-store-and-explore-download-spn-secret"}}},{"name":"FLINK_CONF_DIR","value":"/opt/flink/conf-ude"},{"name":"HADOOP_OPTIONAL_TOOLS","value":"hadoop-azure"},{"name":"BOOTSTRAP_SERVERS","value":"pkc-vr3yjz.westeurope.azure.confluent.cloud:9092"},{"name":"SCHEMA_REGISTRY_URL","value":"https://psrc-j39np.westeurope.azure.confluent.cloud"},{"name":"KAFKA_GDC_SOURCE_TOPIC_NAME","value":"private.ude-str.bb3082d0-bd06-49a0-a83f-39223f75706b.input.json"},{"name":"KAFKA_VMS_SOURCE_TOPIC_NAME","value":"private.ude-str.vms-connector.flattened.json"},{"name":"KAFKA_SINK_TOPIC_NAME","value":"public.ude-str.bb3082d0-bd06-49a0-a83f-39223f75706b.output.json"},{"name":"KAFKA_NON_RETRYABLE_DLQ_TOPIC_NAME","value":"private.ude-str.bb3082d0-bd06-49a0-a83f-39223f75706b.non-retryable.dlq.json"},{"name":"FLINK_GROUP_ID","value":"gdc-bb3082d0-bd06-49a0-a83f-39223f75706b"},{"name":"DB_URL","value":"***************************************************************************************************"},{"name":"DB_USER_NAME","value":"psqladmin"},{"name":"DB_CLAIMS_DATABASE_NAME","value":"management-api"},{"name":"DB_PROCESSING_CLAIM_TABLE_NAME","value":"processing_claim"},{"name":"DB_VDC_DATABASE_NAME","value":"signals"},{"name":"DB_VDC_RULES_TABLE_NAME","value":"normalization_rule"},{"name":"API_USE_CASE_ID","value":"bb3082d0-bd06-49a0-a83f-39223f75706b"},{"name":"DATA_ORDER_RETENTION_DAYS","value":"1"},{"name":"ENRICHMENT_CONFIG","value":"[{\"schemaId\":\"786e910a-6cad-4db9-8cf0-399f25ce4217\",\"enrichmentData\":[{\"source\":\"private.ude-str.vms-connector.flattened.json\",\"type\":\"kafka\",\"fields\":[{\"sourceName\":\"vin\",\"outputName\":\"VIN\"}]}]}]"},{"name":"FCT_RULES","value":"[{}]"}],"envFrom":[{"configMapRef":{"name":"deployment-service-store-and-explore-config"}},{"configMapRef":{"name":"deployment-service-ude-flink-storage-config"}}],"name":"flink-main-container","securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"runAsNonRoot":true,"runAsUser":9999},"volumeMounts":[{"mountPath":"/opt/flink/conf-ude","name":"ude-flink-conf-temp"},{"mountPath":"/opt/flink/log","name":"flink-logs"},{"mountPath":"/opt/flink/downloads","name":"downloads"}]}],"initContainers":[{"command":["/bin/sh","/opt/ude/scripts/init-flink.sh"],"env":[{"name":"FLINK_APPLICATION_NAME","value":"gdc-bb3082d0-bd06-49a0-a83f-39223f75706b"},{"name":"AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-store-and-explore-upload-spn-secret"}}},{"name":"AZURE_UDE_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-ude-flink-storage-spn-secret","optional":true}}}],"envFrom":[{"configMapRef":{"name":"deployment-service-store-and-explore-config"}},{"configMapRef":{"name":"deployment-service-ude-flink-storage-config"}}],"image":"busybox","name":"flink-init-container","securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"runAsNonRoot":true,"runAsUser":9999},"volumeMounts":[{"mountPath":"/opt/ude/scripts","name":"ude-shell-scripts"},{"mountPath":"/tmp/ude/flink-custom-configs","name":"ude-flink-custom-configs"},{"mountPath":"/tmp/ude/flink-devops-tuning","name":"ude-flink-devops-tuning-configs"},{"mountPath":"/tmp/ude/flink-conf","name":"ude-flink-conf-temp"},{"mountPath":"/tmp/real-flink-config","name":"flink-config-volume"}]}],"volumes":[{"configMap":{"name":"deployment-service-ude-shell-scripts"},"name":"ude-shell-scripts"},{"configMap":{"name":"deployment-service-flink-conf-yaml"},"name":"ude-flink-custom-configs"},{"configMap":{"name":"devops-tuning-gdc-bb3082d0-bd06-49a0-a83f-39223f75706b","optional":true},"name":"ude-flink-devops-tuning-configs"},{"emptyDir":{},"name":"ude-flink-conf-temp"},{"emptyDir":{},"name":"flink-logs"},{"emptyDir":{},"name":"downloads"}]}},"serviceAccount":"deployment-service","taskManager":{"resource":{"cpu":6,"memory":"4Gi"}}}}
  creationTimestamp: "2025-05-26T13:10:49Z"
  finalizers:
  - flinkdeployments.flink.apache.org/finalizer
  generation: 2
  labels:
    argocd.argoproj.io/instance: str-int_int-flink-playground
  name: gdc-read-replica-test
  namespace: str-int
  resourceVersion: "**********"
  uid: 9bdca7b9-1185-4060-8bd4-00572a1843c2
spec:
  flinkConfiguration:
    _ude.pipeline.dataOrderId: bb3082d0-bd06-49a0-a83f-39223f75706b
    _ude.pipeline.name: StreamingPipeline.*********.Automation
    _ude.pipeline.projectId: 0c6a9ec1-e362-4e56-9ad3-47ff54d89d5c
    _ude.pipeline.revision: "1"
    kubernetes.container.image.pull-secrets: mega-container-registry-secret
    *********************************: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/
    kubernetes.taskmanager.annotations: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/
    state.checkpoints.dir: UDE_REMOVED
    state.savepoints.dir: UDE_REMOVED
  flinkVersion: v1_20
  image: crmegahubwesteurope.azurecr.io/str-int/gdc-job:main
  imagePullPolicy: Always
  ingress:
    annotations:
      nginx.ingress.kubernetes.io/auth-realm: Authentication Required - Flink Playground
      nginx.ingress.kubernetes.io/auth-secret: deployment-service-flink-basic-auth
      nginx.ingress.kubernetes.io/auth-type: basic
      nginx.ingress.kubernetes.io/rewrite-target: /$2
    className: nginx
    template: str-int.apps.mega.cariad.cloud/{{name}}(/|$)(.*)
  job:
    args: []
    entryClass: org.ude.GdcTransformationJob
    jarURI: local:///opt/flink/lib/flink-job-all.jar
    parallelism: 1
    state: running
    upgradeMode: savepoint
  jobManager:
    replicas: 1
    resource:
      cpu: 1
      memory: 1Gi
  logConfiguration:
    logback-console.xml: |
      <configuration>
        <property name="pattern" value="%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread) %cyan(%-12.36logger{36}) %msg%n" />
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.out</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>ERROR</level>
                <onMatch>DENY</onMatch>
            </filter>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>WARN</level>
                <onMatch>DENY</onMatch>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <appender name="STDERR" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.err</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>WARN</level>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <root level="INFO">
            <appender-ref ref="STDOUT" />
            <appender-ref ref="STDERR" />
        </root>

        <!-- Specific loggers -->
        <logger name="akka" level="INFO"/>
        <logger name="org.apache.kafka" level="INFO"/>
        <logger name="org.apache.hadoop" level="INFO"/>
        <logger name="org.apache.zookeeper" level="INFO"/>
        <logger name="org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline" level="OFF"/>
      </configuration>
  mode: native
  podTemplate:
    metadata:
      labels:
        app.kubernetes.io/name: gdc-bb3082d0-bd06-49a0-a83f-39223f75706b
        dataOrderId: bb3082d0-bd06-49a0-a83f-39223f75706b
        jobType: gdc-job
        pipelineName: StreamingPipeline.*********.Automation
        projectId: 0c6a9ec1-e362-4e56-9ad3-47ff54d89d5c
    spec:
      containers:
      - env:
        - name: SCHEMA_REGISTRY_API_KEY
          valueFrom:
            secretKeyRef:
              key: confluent_schema_registry_key
              name: deployment-service-gdc-job-schema-registry-secret
        - name: SCHEMA_REGISTRY_API_SECRET
          valueFrom:
            secretKeyRef:
              key: confluent_schema_registry_secret
              name: deployment-service-gdc-job-schema-registry-secret
        - name: KAFKA_CLUSTER_API_SECRET
          valueFrom:
            secretKeyRef:
              key: confluent_kafka_api_secret
              name: deployment-service-gdc-job-kafka-credentials
        - name: KAFKA_CLUSTER_API_KEY
          valueFrom:
            secretKeyRef:
              key: confluent_kafka_api_key
              name: deployment-service-gdc-job-kafka-credentials
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: postgresql_admin_password
              name: deployment-service-db-password
        - name: AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: azure_credential_client_secret
              name: deployment-service-store-and-explore-upload-spn-secret
        - name: AZURE_DOWNLOAD_CREDENTIAL_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: azure_credential_client_secret
              name: deployment-service-store-and-explore-download-spn-secret
        - name: FLINK_CONF_DIR
          value: /opt/flink/conf-ude
        - name: HADOOP_OPTIONAL_TOOLS
          value: hadoop-azure
        - name: BOOTSTRAP_SERVERS
          value: pkc-vr3yjz.westeurope.azure.confluent.cloud:9092
        - name: SCHEMA_REGISTRY_URL
          value: https://psrc-j39np.westeurope.azure.confluent.cloud
        - name: KAFKA_GDC_SOURCE_TOPIC_NAME
          value: private.ude-str.bb3082d0-bd06-49a0-a83f-39223f75706b.input.json
        - name: KAFKA_VMS_SOURCE_TOPIC_NAME
          value: private.ude-str.vms-connector.flattened.json
        - name: KAFKA_SINK_TOPIC_NAME
          value: public.ude-str.bb3082d0-bd06-49a0-a83f-39223f75706b.output.json
        - name: KAFKA_NON_RETRYABLE_DLQ_TOPIC_NAME
          value: private.ude-str.bb3082d0-bd06-49a0-a83f-39223f75706b.non-retryable.dlq.json
        - name: FLINK_GROUP_ID
          value: gdc-bb3082d0-bd06-49a0-a83f-39223f75706b
        - name: DB_URL
          value: ***************************************************************************************************
        - name: DB_USER_NAME
          value: psqladmin
        - name: DB_CLAIMS_DATABASE_NAME
          value: management-api
        - name: DB_PROCESSING_CLAIM_TABLE_NAME
          value: processing_claim
        - name: DB_VDC_DATABASE_NAME
          value: signals
        - name: DB_VDC_RULES_TABLE_NAME
          value: normalization_rule
        - name: API_USE_CASE_ID
          value: bb3082d0-bd06-49a0-a83f-39223f75706b
        - name: DATA_ORDER_RETENTION_DAYS
          value: "1"
        - name: ENRICHMENT_CONFIG
          value: '[{"schemaId":"786e910a-6cad-4db9-8cf0-399f25ce4217","enrichmentData":[{"source":"private.ude-str.vms-connector.flattened.json","type":"kafka","fields":[{"sourceName":"vin","outputName":"VIN"}]}]}]'
        - name: FCT_RULES
          value: '[{}]'
        envFrom:
        - configMapRef:
            name: deployment-service-store-and-explore-config
        - configMapRef:
            name: deployment-service-ude-flink-storage-config
        name: flink-main-container
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          runAsUser: 9999
        volumeMounts:
        - mountPath: /opt/flink/conf-ude
          name: ude-flink-conf-temp
        - mountPath: /opt/flink/log
          name: flink-logs
        - mountPath: /opt/flink/downloads
          name: downloads
      initContainers:
      - command:
        - /bin/sh
        - /opt/ude/scripts/init-flink.sh
        env:
        - name: FLINK_APPLICATION_NAME
          value: gdc-bb3082d0-bd06-49a0-a83f-39223f75706b
        - name: AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: azure_credential_client_secret
              name: deployment-service-store-and-explore-upload-spn-secret
        - name: AZURE_UDE_CREDENTIAL_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: azure_credential_client_secret
              name: deployment-service-ude-flink-storage-spn-secret
              optional: true
        envFrom:
        - configMapRef:
            name: deployment-service-store-and-explore-config
        - configMapRef:
            name: deployment-service-ude-flink-storage-config
        image: busybox
        name: flink-init-container
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          runAsUser: 9999
        volumeMounts:
        - mountPath: /opt/ude/scripts
          name: ude-shell-scripts
        - mountPath: /tmp/ude/flink-custom-configs
          name: ude-flink-custom-configs
        - mountPath: /tmp/ude/flink-devops-tuning
          name: ude-flink-devops-tuning-configs
        - mountPath: /tmp/ude/flink-conf
          name: ude-flink-conf-temp
        - mountPath: /tmp/real-flink-config
          name: flink-config-volume
      volumes:
      - configMap:
          name: deployment-service-ude-shell-scripts
        name: ude-shell-scripts
      - configMap:
          name: deployment-service-flink-conf-yaml
        name: ude-flink-custom-configs
      - configMap:
          name: devops-tuning-gdc-bb3082d0-bd06-49a0-a83f-39223f75706b
          optional: true
        name: ude-flink-devops-tuning-configs
      - emptyDir: {}
        name: ude-flink-conf-temp
      - emptyDir: {}
        name: flink-logs
      - emptyDir: {}
        name: downloads
  serviceAccount: deployment-service
  taskManager:
    resource:
      cpu: 6
      memory: 4Gi
status:
  clusterInfo: {}
  error: '{"type":"org.apache.flink.kubernetes.operator.exception.DeploymentFailedException","message":"back-off
    5m0s restarting failed container=flink-main-container pod=gdc-read-replica-test-867c857c98-gttlj_str-int(a2f041f2-5c65-492b-bd28-24abea50e4ad)","additionalMetadata":{"reason":"CrashLoopBackOff"},"throwableList":[]}'
  jobManagerDeploymentStatus: ERROR
  jobStatus:
    checkpointInfo:
      lastPeriodicCheckpointTimestamp: 0
    jobId: 6af55e94c5e14f2e0ca218ee0bdb30f3
    savepointInfo:
      lastPeriodicSavepointTimestamp: 0
      savepointHistory: []
    state: RECONCILING
  lifecycleState: DEPLOYED
  observedGeneration: 2
  reconciliationStatus:
    lastReconciledSpec: '{"spec":{"job":{"jarURI":"local:///opt/flink/lib/flink-job-all.jar","parallelism":1,"entryClass":"org.ude.GdcTransformationJob","args":[],"state":"running","savepointTriggerNonce":null,"initialSavepointPath":null,"checkpointTriggerNonce":null,"upgradeMode":"stateless","allowNonRestoredState":null,"savepointRedeployNonce":null,"autoscalerResetNonce":null},"restartNonce":null,"flinkConfiguration":{"_ude.pipeline.dataOrderId":"bb3082d0-bd06-49a0-a83f-39223f75706b","_ude.pipeline.name":"StreamingPipeline.*********.Automation","_ude.pipeline.projectId":"0c6a9ec1-e362-4e56-9ad3-47ff54d89d5c","_ude.pipeline.revision":"1","kubernetes.container.image.pull-secrets":"mega-container-registry-secret","*********************************":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","kubernetes.taskmanager.annotations":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","state.checkpoints.dir":"UDE_REMOVED","state.savepoints.dir":"UDE_REMOVED"},"image":"crmegahubwesteurope.azurecr.io/str-int/gdc-job:main","imagePullPolicy":"Always","serviceAccount":"deployment-service","flinkVersion":"v1_20","ingress":{"template":"str-int.apps.mega.cariad.cloud/{{name}}(/|$)(.*)","className":"nginx","annotations":{"nginx.ingress.kubernetes.io/auth-realm":"Authentication
      Required - Flink Playground","nginx.ingress.kubernetes.io/auth-secret":"deployment-service-flink-basic-auth","nginx.ingress.kubernetes.io/auth-type":"basic","nginx.ingress.kubernetes.io/rewrite-target":"/$2"},"labels":null,"tls":null},"podTemplate":{"metadata":{"labels":{"app.kubernetes.io/name":"gdc-bb3082d0-bd06-49a0-a83f-39223f75706b","dataOrderId":"bb3082d0-bd06-49a0-a83f-39223f75706b","jobType":"gdc-job","pipelineName":"StreamingPipeline.*********.Automation","projectId":"0c6a9ec1-e362-4e56-9ad3-47ff54d89d5c"}},"spec":{"containers":[{"env":[{"name":"SCHEMA_REGISTRY_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_schema_registry_key","name":"deployment-service-gdc-job-schema-registry-secret"}}},{"name":"SCHEMA_REGISTRY_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_schema_registry_secret","name":"deployment-service-gdc-job-schema-registry-secret"}}},{"name":"KAFKA_CLUSTER_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_secret","name":"deployment-service-gdc-job-kafka-credentials"}}},{"name":"KAFKA_CLUSTER_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_key","name":"deployment-service-gdc-job-kafka-credentials"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"postgresql_admin_password","name":"deployment-service-db-password"}}},{"name":"AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-store-and-explore-upload-spn-secret"}}},{"name":"AZURE_DOWNLOAD_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-store-and-explore-download-spn-secret"}}},{"name":"FLINK_CONF_DIR","value":"/opt/flink/conf-ude"},{"name":"HADOOP_OPTIONAL_TOOLS","value":"hadoop-azure"},{"name":"BOOTSTRAP_SERVERS","value":"pkc-vr3yjz.westeurope.azure.confluent.cloud:9092"},{"name":"SCHEMA_REGISTRY_URL","value":"https://psrc-j39np.westeurope.azure.confluent.cloud"},{"name":"KAFKA_GDC_SOURCE_TOPIC_NAME","value":"private.ude-str.bb3082d0-bd06-49a0-a83f-39223f75706b.input.json"},{"name":"KAFKA_VMS_SOURCE_TOPIC_NAME","value":"private.ude-str.vms-connector.flattened.json"},{"name":"KAFKA_SINK_TOPIC_NAME","value":"public.ude-str.bb3082d0-bd06-49a0-a83f-39223f75706b.output.json"},{"name":"KAFKA_NON_RETRYABLE_DLQ_TOPIC_NAME","value":"private.ude-str.bb3082d0-bd06-49a0-a83f-39223f75706b.non-retryable.dlq.json"},{"name":"FLINK_GROUP_ID","value":"gdc-bb3082d0-bd06-49a0-a83f-39223f75706b"},{"name":"DB_URL","value":"***************************************************************************************************"},{"name":"DB_USER_NAME","value":"psqladmin"},{"name":"DB_CLAIMS_DATABASE_NAME","value":"management-api"},{"name":"DB_PROCESSING_CLAIM_TABLE_NAME","value":"processing_claim"},{"name":"DB_VDC_DATABASE_NAME","value":"signals"},{"name":"DB_VDC_RULES_TABLE_NAME","value":"normalization_rule"},{"name":"API_USE_CASE_ID","value":"bb3082d0-bd06-49a0-a83f-39223f75706b"},{"name":"DATA_ORDER_RETENTION_DAYS","value":"1"},{"name":"ENRICHMENT_CONFIG","value":"[{\"schemaId\":\"786e910a-6cad-4db9-8cf0-399f25ce4217\",\"enrichmentData\":[{\"source\":\"private.ude-str.vms-connector.flattened.json\",\"type\":\"kafka\",\"fields\":[{\"sourceName\":\"vin\",\"outputName\":\"VIN\"}]}]}]"},{"name":"FCT_RULES","value":"[{}]"}],"envFrom":[{"configMapRef":{"name":"deployment-service-store-and-explore-config"}},{"configMapRef":{"name":"deployment-service-ude-flink-storage-config"}}],"name":"flink-main-container","securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"runAsNonRoot":true,"runAsUser":9999},"volumeMounts":[{"mountPath":"/opt/flink/conf-ude","name":"ude-flink-conf-temp"},{"mountPath":"/opt/flink/log","name":"flink-logs"},{"mountPath":"/opt/flink/downloads","name":"downloads"}]}],"initContainers":[{"command":["/bin/sh","/opt/ude/scripts/init-flink.sh"],"env":[{"name":"FLINK_APPLICATION_NAME","value":"gdc-bb3082d0-bd06-49a0-a83f-39223f75706b"},{"name":"AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-store-and-explore-upload-spn-secret"}}},{"name":"AZURE_UDE_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-ude-flink-storage-spn-secret","optional":true}}}],"envFrom":[{"configMapRef":{"name":"deployment-service-store-and-explore-config"}},{"configMapRef":{"name":"deployment-service-ude-flink-storage-config"}}],"image":"busybox","name":"flink-init-container","securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"runAsNonRoot":true,"runAsUser":9999},"volumeMounts":[{"mountPath":"/opt/ude/scripts","name":"ude-shell-scripts"},{"mountPath":"/tmp/ude/flink-custom-configs","name":"ude-flink-custom-configs"},{"mountPath":"/tmp/ude/flink-devops-tuning","name":"ude-flink-devops-tuning-configs"},{"mountPath":"/tmp/ude/flink-conf","name":"ude-flink-conf-temp"},{"mountPath":"/tmp/real-flink-config","name":"flink-config-volume"}]}],"volumes":[{"configMap":{"name":"deployment-service-ude-shell-scripts"},"name":"ude-shell-scripts"},{"configMap":{"name":"deployment-service-flink-conf-yaml"},"name":"ude-flink-custom-configs"},{"configMap":{"name":"devops-tuning-gdc-bb3082d0-bd06-49a0-a83f-39223f75706b","optional":true},"name":"ude-flink-devops-tuning-configs"},{"emptyDir":{},"name":"ude-flink-conf-temp"},{"emptyDir":{},"name":"flink-logs"},{"emptyDir":{},"name":"downloads"}]}},"jobManager":{"resource":{"cpu":1.0,"memory":"1Gi","ephemeralStorage":null},"replicas":1,"podTemplate":null},"taskManager":{"resource":{"cpu":6.0,"memory":"4Gi","ephemeralStorage":null},"replicas":null,"podTemplate":null},"logConfiguration":{"logback-console.xml":"<configuration>\n  <property
      name=\"pattern\" value=\"%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread)
      %cyan(%-12.36logger{36}) %msg%n\" />\n  <appender name=\"STDOUT\" class=\"ch.qos.logback.core.ConsoleAppender\">\n      <target>System.out</target>\n      <immediateFlush>true</immediateFlush>\n      <filter
      class=\"ch.qos.logback.classic.filter.LevelFilter\">\n          <level>ERROR</level>\n          <onMatch>DENY</onMatch>\n      </filter>\n      <filter
      class=\"ch.qos.logback.classic.filter.LevelFilter\">\n          <level>WARN</level>\n          <onMatch>DENY</onMatch>\n      </filter>\n      <encoder>\n          <pattern>${pattern}</pattern>\n      </encoder>\n  </appender>\n\n  <appender
      name=\"STDERR\" class=\"ch.qos.logback.core.ConsoleAppender\">\n      <target>System.err</target>\n      <immediateFlush>true</immediateFlush>\n      <filter
      class=\"ch.qos.logback.classic.filter.ThresholdFilter\">\n          <level>WARN</level>\n      </filter>\n      <encoder>\n          <pattern>${pattern}</pattern>\n      </encoder>\n  </appender>\n\n  <root
      level=\"INFO\">\n      <appender-ref ref=\"STDOUT\" />\n      <appender-ref
      ref=\"STDERR\" />\n  </root>\n\n  <!-- Specific loggers -->\n  <logger name=\"akka\"
      level=\"INFO\"/>\n  <logger name=\"org.apache.kafka\" level=\"INFO\"/>\n  <logger
      name=\"org.apache.hadoop\" level=\"INFO\"/>\n  <logger name=\"org.apache.zookeeper\"
      level=\"INFO\"/>\n  <logger name=\"org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline\"
      level=\"OFF\"/>\n</configuration>\n"},"mode":"native"},"resource_metadata":{"apiVersion":"flink.apache.org/v1beta1","firstDeployment":true}}'
    reconciliationTimestamp: 1748265050850
    state: DEPLOYED
  taskManager:
    labelSelector: component=taskmanager,app=gdc-read-replica-test
    replicas: 1
