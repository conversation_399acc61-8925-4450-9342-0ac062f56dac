apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  annotations:
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"flink.apache.org/v1beta1","kind":"FlinkDeployment","metadata":{"annotations":{},"name":"local-flink-job","namespace":"str-int"},"spec":{"flinkConfiguration":{"execution.checkpointing.dir":"abfss://<EMAIL>/local-flink-job/checkpoints","execution.checkpointing.externalized-checkpoint-retention":"RETAIN_ON_CANCELLATION","execution.checkpointing.savepoint-dir":"abfss://<EMAIL>/local-flink-job/savepoints","fs.azure.abfs.endpoint":"udestrinteuflinkstorage.dfs.core.windows.net","fs.azure.account.auth.type":"OAuth","fs.azure.account.oauth.provider.type":"org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider","fs.azure.account.oauth2.client.endpoint":"https://login.microsoftonline.com/c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c/oauth2/token","fs.azure.account.oauth2.client.id":"253d76fe-1405-4cf7-ae71-e7f65666142f","fs.azure.account.oauth2.client.secret":"${AZURE_UDE_CREDENTIAL_CLIENT_SECRET}","fs.azure.data.blocks.buffer":"bytebuffer","fs.defaultFS":"abfss://<EMAIL>","state.backend.incremental":"true","state.backend.type":"rocksdb","taskmanager.numberOfTaskSlots":"2"},"flinkVersion":"v1_20","image":"flink:1.20","job":{"entryClass":"org.apache.flink.streaming.examples.statemachine.StateMachineExample","jarURI":"local:///opt/flink/examples/streaming/StateMachineExample.jar","parallelism":1,"upgradeMode":"stateless"},"jobManager":{"resource":{"cpu":1,"memory":"2048m"}},"mode":"native","podTemplate":{"spec":{"containers":[{"env":[{"name":"AZURE_UDE_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-flink-storage-spn-secret"}}}],"name":"flink-main-container","securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"runAsNonRoot":true,"runAsUser":9999}}]}},"serviceAccount":"deployment-service","taskManager":{"resource":{"cpu":1,"memory":"2048m"}}}}
  creationTimestamp: "2025-06-04T08:45:05Z"
  finalizers:
  - flinkdeployments.flink.apache.org/finalizer
  generation: 3
  name: local-flink-job
  namespace: str-int
  resourceVersion: "**********"
  uid: 8a4b8b0d-2487-4d31-882e-459cc35266ee
spec:
  flinkConfiguration:
    execution.checkpointing.dir: abfss://<EMAIL>/local-flink-job/checkpoints
    execution.checkpointing.externalized-checkpoint-retention: RETAIN_ON_CANCELLATION
    execution.checkpointing.savepoint-dir: abfss://<EMAIL>/local-flink-job/savepoints
    fs.azure.abfs.endpoint: udestrinteuflinkstorage.dfs.core.windows.net
    fs.azure.account.auth.type: OAuth
    fs.azure.account.oauth.provider.type: org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider
    fs.azure.account.oauth2.client.endpoint: https://login.microsoftonline.com/c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c/oauth2/token
    fs.azure.account.oauth2.client.id: 253d76fe-1405-4cf7-ae71-e7f65666142f
    fs.azure.account.oauth2.client.secret: ${AZURE_UDE_CREDENTIAL_CLIENT_SECRET}
    fs.azure.data.blocks.buffer: bytebuffer
    fs.defaultFS: abfss://<EMAIL>
    state.backend.incremental: "true"
    state.backend.type: rocksdb
    taskmanager.numberOfTaskSlots: "2"
  flinkVersion: v1_20
  image: flink:1.20
  job:
    args: []
    entryClass: org.apache.flink.streaming.examples.statemachine.StateMachineExample
    jarURI: local:///opt/flink/examples/streaming/StateMachineExample.jar
    parallelism: 1
    state: running
    upgradeMode: stateless
  jobManager:
    replicas: 1
    resource:
      cpu: 1
      memory: 2048m
  mode: native
  podTemplate:
    spec:
      containers:
      - env:
        - name: AZURE_UDE_CREDENTIAL_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: azure_credential_client_secret
              name: deployment-service-flink-storage-spn-secret
        name: flink-main-container
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          runAsUser: 9999
  serviceAccount: deployment-service
  taskManager:
    resource:
      cpu: 1
      memory: 2048m
status:
  clusterInfo: {}
  error: '{"type":"org.apache.flink.kubernetes.operator.exception.DeploymentFailedException","message":"back-off
    5m0s restarting failed container=flink-main-container pod=local-flink-job-676dc598f8-269cc_str-int(b129052b-9d52-4f1a-9c36-91e7cbbf8f34)","additionalMetadata":{"reason":"CrashLoopBackOff"},"throwableList":[]}'
  jobManagerDeploymentStatus: ERROR
  jobStatus:
    checkpointInfo:
      lastPeriodicCheckpointTimestamp: 0
    jobId: 1d5260d4449e2384e77bf18d574f3b07
    savepointInfo:
      lastPeriodicSavepointTimestamp: 0
      savepointHistory: []
    state: RECONCILING
  lifecycleState: DEPLOYED
  observedGeneration: 3
  reconciliationStatus:
    lastReconciledSpec: '{"spec":{"job":{"jarURI":"local:///opt/flink/examples/streaming/StateMachineExample.jar","parallelism":1,"entryClass":"org.apache.flink.streaming.examples.statemachine.StateMachineExample","args":[],"state":"running","savepointTriggerNonce":null,"initialSavepointPath":null,"checkpointTriggerNonce":null,"upgradeMode":"stateless","allowNonRestoredState":null,"savepointRedeployNonce":null,"autoscalerResetNonce":null},"restartNonce":null,"flinkConfiguration":{"execution.checkpointing.dir":"abfss://<EMAIL>/local-flink-job/checkpoints","execution.checkpointing.externalized-checkpoint-retention":"RETAIN_ON_CANCELLATION","execution.checkpointing.savepoint-dir":"abfss://<EMAIL>/local-flink-job/savepoints","fs.azure.abfs.endpoint":"udestrinteuflinkstorage.dfs.core.windows.net","fs.azure.account.auth.type":"OAuth","fs.azure.account.oauth.provider.type":"org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider","fs.azure.account.oauth2.client.endpoint":"https://login.microsoftonline.com/c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c/oauth2/token","fs.azure.account.oauth2.client.id":"253d76fe-1405-4cf7-ae71-e7f65666142f","fs.azure.account.oauth2.client.secret":"${AZURE_UDE_CREDENTIAL_CLIENT_SECRET}","fs.azure.data.blocks.buffer":"bytebuffer","fs.defaultFS":"abfss://<EMAIL>","state.backend.incremental":"true","state.backend.type":"rocksdb","taskmanager.numberOfTaskSlots":"2"},"image":"flink:1.20","imagePullPolicy":null,"serviceAccount":"deployment-service","flinkVersion":"v1_20","ingress":null,"podTemplate":{"spec":{"containers":[{"env":[{"name":"AZURE_UDE_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-flink-storage-spn-secret"}}}],"name":"flink-main-container","securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"runAsNonRoot":true,"runAsUser":9999}}]}},"jobManager":{"resource":{"cpu":1.0,"memory":"2048m","ephemeralStorage":null},"replicas":1,"podTemplate":null},"taskManager":{"resource":{"cpu":1.0,"memory":"2048m","ephemeralStorage":null},"replicas":null,"podTemplate":null},"logConfiguration":null,"mode":"native"},"resource_metadata":{"apiVersion":"flink.apache.org/v1beta1","firstDeployment":true}}'
    reconciliationTimestamp: *************
    state: DEPLOYED
  taskManager:
    labelSelector: component=taskmanager,app=local-flink-job
    replicas: 1
