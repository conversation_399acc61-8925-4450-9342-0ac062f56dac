apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  annotations:
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"flink.apache.org/v1beta1","kind":"FlinkDeployment","metadata":{"annotations":{},"labels":{"argocd.argoproj.io/instance":"str-int_int-flink-playground"},"name":"flink-passthrough-application-mode","namespace":"str-int"},"spec":{"flinkConfiguration":{"classloader.resolve-order":"parent-first","env.java.opts.all":"--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED","job.autoscaler.enabled":"true","job.autoscaler.memory.tuning.enabled":"true","job.autoscaler.memory.tuning.overhead":"1","job.autoscaler.metrics.window":"1m","job.autoscaler.scaling.enabled":"true","job.autoscaler.stabilization.interval":"1m","jobmanager.memory.process.size":"1GB","jobmanager.scheduler":"adaptive","kubernetes.container.image.pull-secrets":"mega-container-registry-secret","kubernetes.jobmanager.annotations":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","kubernetes.taskmanager.annotations":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","metrics.reporter.prom.factory.class":"org.apache.flink.metrics.prometheus.PrometheusReporterFactory","metrics.reporter.prom.port":"9249","rest.flamegraph.enabled":"true","rest.flamegraph.refresh-interval":"10 m","rest.profiling.enabled":"true","taskmanager.memory.process.size":"3GB","taskmanager.numberOfTaskSlots":"5"},"flinkVersion":"v1_20","image":"crmegahubwesteurope.azurecr.io/str-int/passthrough-job:1.0-SNAPSHOT","ingress":{"annotations":{"nginx.ingress.kubernetes.io/auth-realm":"Authentication Required - Flink Playground","nginx.ingress.kubernetes.io/auth-secret":"ude-str-flink-basic-auth","nginx.ingress.kubernetes.io/auth-type":"basic","nginx.ingress.kubernetes.io/rewrite-target":"/$2"},"className":"nginx","template":"str-int.apps.mega.cariad.cloud/{{name}}(/|$)(.*)"},"job":{"entryClass":"org.ude.PassthroughJob","jarURI":"local:///opt/flink/lib/flink-job-all.jar","parallelism":1,"upgradeMode":"stateless"},"jobManager":{"resource":{"cpu":1,"memory":"4Gi"}},"logConfiguration":{"logback-console.xml":"\u003cconfiguration\u003e\n  \u003cproperty name=\"pattern\" value=\"%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread) %cyan(%-12.36logger{36}) %msg%n\" /\u003e\n  \u003cappender name=\"STDOUT\" class=\"ch.qos.logback.core.ConsoleAppender\"\u003e\n      \u003ctarget\u003eSystem.out\u003c/target\u003e\n      \u003cimmediateFlush\u003etrue\u003c/immediateFlush\u003e\n      \u003cfilter class=\"ch.qos.logback.classic.filter.LevelFilter\"\u003e\n          \u003clevel\u003eERROR\u003c/level\u003e\n          \u003conMatch\u003eDENY\u003c/onMatch\u003e\n      \u003c/filter\u003e\n      \u003cfilter class=\"ch.qos.logback.classic.filter.LevelFilter\"\u003e\n          \u003clevel\u003eWARN\u003c/level\u003e\n          \u003conMatch\u003eDENY\u003c/onMatch\u003e\n      \u003c/filter\u003e\n      \u003cencoder\u003e\n          \u003cpattern\u003e${pattern}\u003c/pattern\u003e\n      \u003c/encoder\u003e\n  \u003c/appender\u003e\n\n  \u003cappender name=\"STDERR\" class=\"ch.qos.logback.core.ConsoleAppender\"\u003e\n      \u003ctarget\u003eSystem.err\u003c/target\u003e\n      \u003cimmediateFlush\u003etrue\u003c/immediateFlush\u003e\n      \u003cfilter class=\"ch.qos.logback.classic.filter.ThresholdFilter\"\u003e\n          \u003clevel\u003eWARN\u003c/level\u003e\n      \u003c/filter\u003e\n      \u003cencoder\u003e\n          \u003cpattern\u003e${pattern}\u003c/pattern\u003e\n      \u003c/encoder\u003e\n  \u003c/appender\u003e\n\n  \u003croot level=\"INFO\"\u003e\n      \u003cappender-ref ref=\"STDOUT\" /\u003e\n      \u003cappender-ref ref=\"STDERR\" /\u003e\n  \u003c/root\u003e\n\n  \u003c!-- Specific loggers --\u003e\n  \u003clogger name=\"akka\" level=\"INFO\"/\u003e\n  \u003clogger name=\"org.apache.kafka\" level=\"INFO\"/\u003e\n  \u003clogger name=\"org.apache.hadoop\" level=\"INFO\"/\u003e\n  \u003clogger name=\"org.apache.zookeeper\" level=\"INFO\"/\u003e\n  \u003clogger name=\"org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline\" level=\"OFF\"/\u003e\n\u003c/configuration\u003e\n"},"mode":"native","podTemplate":{"metadata":{"labels":{"app.kubernetes.io/name":"flink-passthrough-application-mode"}},"spec":{"containers":[{"env":[{"name":"env.java.opts.all","value":"--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED"},{"name":"BOOTSTRAP_SERVERS","value":"pkc-vr3yjz.westeurope.azure.confluent.cloud:9092"},{"name":"SCHEMA_REGISTRY_URL","value":"https://psrc-j39np.westeurope.azure.confluent.cloud"},{"name":"SCHEMA_REGISTRY_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_schema_registry_key","name":"ude-str-flink-gdc-job-schema-registry-secret"}}},{"name":"SCHEMA_REGISTRY_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_schema_registry_secret","name":"ude-str-flink-gdc-job-schema-registry-secret"}}},{"name":"KAFKA_CLUSTER_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_secret","name":"ude-str-flink-gdc-job-kafka-api-secret"}}},{"name":"KAFKA_CLUSTER_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_key","name":"ude-str-flink-gdc-job-kafka-api-secret"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"postgresql_admin_password","name":"ude-str-flink-gdc-job-db-password"}}},{"name":"KAFKA_PASSTHROUGH_SOURCE_TOPIC_NAME","value":"private.ude-str.passthrough.input.json"},{"name":"KAFKA_SINK_TOPIC_NAME","value":"public.ude-str.passthrough.output.json"},{"name":"FLINK_GROUP_ID","value":"flink-job-1"},{"name":"DB_URL","value":"******************************************************************************************"},{"name":"DB_USER_NAME","value":"psqladmin"},{"name":"DB_CLAIMS_DATABASE_NAME","value":"management-api"},{"name":"DB_PROCESSING_CLAIM_TABLE_NAME","value":"processing_claim"},{"name":"DB_VDC_DATABASE_NAME","value":"vdc"},{"name":"DB_VDC_RULE_TABLE_NAME","value":"normalization_rule"},{"name":"API_USE_CASE_ID","value":"passthrough"}],"name":"flink-main-container","securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"runAsNonRoot":true,"runAsUser":1000},"volumeMounts":[{"mountPath":"/opt/flink/log","name":"flink-logs"},{"mountPath":"/opt/flink/downloads","name":"downloads"}]}],"volumes":[{"emptyDir":{},"name":"flink-logs"},{"emptyDir":{},"name":"downloads"}]}},"serviceAccount":"flink","taskManager":{"resource":{"cpu":0.5,"memory":"4Gi"}}}}
  creationTimestamp: "2025-05-09T08:40:32Z"
  finalizers:
  - flinkdeployments.flink.apache.org/finalizer
  generation: 2
  labels:
    argocd.argoproj.io/instance: str-int_int-flink-playground
  name: flink-passthrough-application-mode
  namespace: str-int
  resourceVersion: "**********"
  uid: 713f725d-f092-4b84-91f6-6bf60ba57bf9
spec:
  flinkConfiguration:
    classloader.resolve-order: parent-first
    env.java.opts.all: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED
    job.autoscaler.enabled: "true"
    job.autoscaler.memory.tuning.enabled: "true"
    job.autoscaler.memory.tuning.overhead: "1"
    job.autoscaler.metrics.window: 1m
    job.autoscaler.scaling.enabled: "true"
    job.autoscaler.stabilization.interval: 1m
    jobmanager.memory.process.size: 1GB
    jobmanager.scheduler: adaptive
    kubernetes.container.image.pull-secrets: mega-container-registry-secret
    kubernetes.jobmanager.annotations: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/
    kubernetes.taskmanager.annotations: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/
    metrics.reporter.prom.factory.class: org.apache.flink.metrics.prometheus.PrometheusReporterFactory
    metrics.reporter.prom.port: "9249"
    rest.flamegraph.enabled: "true"
    rest.flamegraph.refresh-interval: 10 m
    rest.profiling.enabled: "true"
    taskmanager.memory.process.size: 3GB
    taskmanager.numberOfTaskSlots: "5"
  flinkVersion: v1_20
  image: crmegahubwesteurope.azurecr.io/str-int/passthrough-job:1.0-SNAPSHOT
  ingress:
    annotations:
      nginx.ingress.kubernetes.io/auth-realm: Authentication Required - Flink Playground
      nginx.ingress.kubernetes.io/auth-secret: ude-str-flink-basic-auth
      nginx.ingress.kubernetes.io/auth-type: basic
      nginx.ingress.kubernetes.io/rewrite-target: /$2
    className: nginx
    template: str-int.apps.mega.cariad.cloud/{{name}}(/|$)(.*)
  job:
    args: []
    entryClass: org.ude.PassthroughJob
    jarURI: local:///opt/flink/lib/flink-job-all.jar
    parallelism: 1
    state: running
    upgradeMode: stateless
  jobManager:
    replicas: 1
    resource:
      cpu: 1
      memory: 4Gi
  logConfiguration:
    logback-console.xml: |
      <configuration>
        <property name="pattern" value="%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread) %cyan(%-12.36logger{36}) %msg%n" />
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.out</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>ERROR</level>
                <onMatch>DENY</onMatch>
            </filter>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>WARN</level>
                <onMatch>DENY</onMatch>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <appender name="STDERR" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.err</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>WARN</level>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <root level="INFO">
            <appender-ref ref="STDOUT" />
            <appender-ref ref="STDERR" />
        </root>

        <!-- Specific loggers -->
        <logger name="akka" level="INFO"/>
        <logger name="org.apache.kafka" level="INFO"/>
        <logger name="org.apache.hadoop" level="INFO"/>
        <logger name="org.apache.zookeeper" level="INFO"/>
        <logger name="org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline" level="OFF"/>
      </configuration>
  mode: native
  podTemplate:
    metadata:
      labels:
        app.kubernetes.io/name: flink-passthrough-application-mode
    spec:
      containers:
      - env:
        - name: env.java.opts.all
          value: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED
        - name: BOOTSTRAP_SERVERS
          value: pkc-vr3yjz.westeurope.azure.confluent.cloud:9092
        - name: SCHEMA_REGISTRY_URL
          value: https://psrc-j39np.westeurope.azure.confluent.cloud
        - name: SCHEMA_REGISTRY_API_KEY
          valueFrom:
            secretKeyRef:
              key: confluent_schema_registry_key
              name: ude-str-flink-gdc-job-schema-registry-secret
        - name: SCHEMA_REGISTRY_API_SECRET
          valueFrom:
            secretKeyRef:
              key: confluent_schema_registry_secret
              name: ude-str-flink-gdc-job-schema-registry-secret
        - name: KAFKA_CLUSTER_API_SECRET
          valueFrom:
            secretKeyRef:
              key: confluent_kafka_api_secret
              name: ude-str-flink-gdc-job-kafka-api-secret
        - name: KAFKA_CLUSTER_API_KEY
          valueFrom:
            secretKeyRef:
              key: confluent_kafka_api_key
              name: ude-str-flink-gdc-job-kafka-api-secret
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: postgresql_admin_password
              name: ude-str-flink-gdc-job-db-password
        - name: KAFKA_PASSTHROUGH_SOURCE_TOPIC_NAME
          value: private.ude-str.passthrough.input.json
        - name: KAFKA_SINK_TOPIC_NAME
          value: public.ude-str.passthrough.output.json
        - name: FLINK_GROUP_ID
          value: flink-job-1
        - name: DB_URL
          value: ******************************************************************************************
        - name: DB_USER_NAME
          value: psqladmin
        - name: DB_CLAIMS_DATABASE_NAME
          value: management-api
        - name: DB_PROCESSING_CLAIM_TABLE_NAME
          value: processing_claim
        - name: DB_VDC_DATABASE_NAME
          value: vdc
        - name: DB_VDC_RULE_TABLE_NAME
          value: normalization_rule
        - name: API_USE_CASE_ID
          value: passthrough
        name: flink-main-container
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          runAsUser: 1000
        volumeMounts:
        - mountPath: /opt/flink/log
          name: flink-logs
        - mountPath: /opt/flink/downloads
          name: downloads
      volumes:
      - emptyDir: {}
        name: flink-logs
      - emptyDir: {}
        name: downloads
  serviceAccount: flink
  taskManager:
    resource:
      cpu: 0.5
      memory: 4Gi
status:
  clusterInfo:
    flink-revision: cb1e7b5 @ 2025-01-28T23:32:02+01:00
    flink-version: 1.20.1
    total-cpu: "1.5"
    total-memory: "**********"
  jobManagerDeploymentStatus: READY
  jobStatus:
    checkpointInfo:
      lastPeriodicCheckpointTimestamp: 0
    jobId: 2ada71ca4ad9cdeb89131fc3d253dbc1
    jobName: flink-passthrough-application-mode
    savepointInfo:
      lastPeriodicSavepointTimestamp: 0
      savepointHistory: []
    startTime: "*************"
    state: RUNNING
    updateTime: "*************"
  lifecycleState: STABLE
  observedGeneration: 2
  reconciliationStatus:
    lastReconciledSpec: '{"spec":{"job":{"jarURI":"local:///opt/flink/lib/flink-job-all.jar","parallelism":1,"entryClass":"org.ude.PassthroughJob","args":[],"state":"running","savepointTriggerNonce":null,"initialSavepointPath":null,"checkpointTriggerNonce":null,"upgradeMode":"stateless","allowNonRestoredState":null,"savepointRedeployNonce":null,"autoscalerResetNonce":null},"restartNonce":null,"flinkConfiguration":{"classloader.resolve-order":"parent-first","env.java.opts.all":"--add-opens=java.base/java.lang=ALL-UNNAMED
      --add-opens=java.base/java.util=ALL-UNNAMED","job.autoscaler.enabled":"true","job.autoscaler.memory.tuning.enabled":"true","job.autoscaler.memory.tuning.overhead":"1","job.autoscaler.metrics.window":"1m","job.autoscaler.scaling.enabled":"true","job.autoscaler.stabilization.interval":"1m","jobmanager.memory.process.size":"1GB","jobmanager.scheduler":"adaptive","kubernetes.container.image.pull-secrets":"mega-container-registry-secret","kubernetes.jobmanager.annotations":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","kubernetes.taskmanager.annotations":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","metrics.reporter.prom.factory.class":"org.apache.flink.metrics.prometheus.PrometheusReporterFactory","metrics.reporter.prom.port":"9249","rest.flamegraph.enabled":"true","rest.flamegraph.refresh-interval":"10
      m","rest.profiling.enabled":"true","taskmanager.memory.process.size":"3GB","taskmanager.numberOfTaskSlots":"5"},"image":"crmegahubwesteurope.azurecr.io/str-int/passthrough-job:1.0-SNAPSHOT","imagePullPolicy":null,"serviceAccount":"flink","flinkVersion":"v1_20","ingress":{"template":"str-int.apps.mega.cariad.cloud/{{name}}(/|$)(.*)","className":"nginx","annotations":{"nginx.ingress.kubernetes.io/auth-realm":"Authentication
      Required - Flink Playground","nginx.ingress.kubernetes.io/auth-secret":"ude-str-flink-basic-auth","nginx.ingress.kubernetes.io/auth-type":"basic","nginx.ingress.kubernetes.io/rewrite-target":"/$2"},"labels":null,"tls":null},"podTemplate":{"metadata":{"labels":{"app.kubernetes.io/name":"flink-passthrough-application-mode"}},"spec":{"containers":[{"env":[{"name":"env.java.opts.all","value":"--add-opens=java.base/java.lang=ALL-UNNAMED
      --add-opens=java.base/java.util=ALL-UNNAMED"},{"name":"BOOTSTRAP_SERVERS","value":"pkc-vr3yjz.westeurope.azure.confluent.cloud:9092"},{"name":"SCHEMA_REGISTRY_URL","value":"https://psrc-j39np.westeurope.azure.confluent.cloud"},{"name":"SCHEMA_REGISTRY_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_schema_registry_key","name":"ude-str-flink-gdc-job-schema-registry-secret"}}},{"name":"SCHEMA_REGISTRY_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_schema_registry_secret","name":"ude-str-flink-gdc-job-schema-registry-secret"}}},{"name":"KAFKA_CLUSTER_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_secret","name":"ude-str-flink-gdc-job-kafka-api-secret"}}},{"name":"KAFKA_CLUSTER_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_key","name":"ude-str-flink-gdc-job-kafka-api-secret"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"postgresql_admin_password","name":"ude-str-flink-gdc-job-db-password"}}},{"name":"KAFKA_PASSTHROUGH_SOURCE_TOPIC_NAME","value":"private.ude-str.passthrough.input.json"},{"name":"KAFKA_SINK_TOPIC_NAME","value":"public.ude-str.passthrough.output.json"},{"name":"FLINK_GROUP_ID","value":"flink-job-1"},{"name":"DB_URL","value":"******************************************************************************************"},{"name":"DB_USER_NAME","value":"psqladmin"},{"name":"DB_CLAIMS_DATABASE_NAME","value":"management-api"},{"name":"DB_PROCESSING_CLAIM_TABLE_NAME","value":"processing_claim"},{"name":"DB_VDC_DATABASE_NAME","value":"vdc"},{"name":"DB_VDC_RULE_TABLE_NAME","value":"normalization_rule"},{"name":"API_USE_CASE_ID","value":"passthrough"}],"name":"flink-main-container","securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"runAsNonRoot":true,"runAsUser":1000},"volumeMounts":[{"mountPath":"/opt/flink/log","name":"flink-logs"},{"mountPath":"/opt/flink/downloads","name":"downloads"}]}],"volumes":[{"emptyDir":{},"name":"flink-logs"},{"emptyDir":{},"name":"downloads"}]}},"jobManager":{"resource":{"cpu":1.0,"memory":"4Gi","ephemeralStorage":null},"replicas":1,"podTemplate":null},"taskManager":{"resource":{"cpu":0.5,"memory":"3221225472","ephemeralStorage":null},"replicas":null,"podTemplate":null},"logConfiguration":{"logback-console.xml":"<configuration>\n  <property
      name=\"pattern\" value=\"%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread)
      %cyan(%-12.36logger{36}) %msg%n\" />\n  <appender name=\"STDOUT\" class=\"ch.qos.logback.core.ConsoleAppender\">\n      <target>System.out</target>\n      <immediateFlush>true</immediateFlush>\n      <filter
      class=\"ch.qos.logback.classic.filter.LevelFilter\">\n          <level>ERROR</level>\n          <onMatch>DENY</onMatch>\n      </filter>\n      <filter
      class=\"ch.qos.logback.classic.filter.LevelFilter\">\n          <level>WARN</level>\n          <onMatch>DENY</onMatch>\n      </filter>\n      <encoder>\n          <pattern>${pattern}</pattern>\n      </encoder>\n  </appender>\n\n  <appender
      name=\"STDERR\" class=\"ch.qos.logback.core.ConsoleAppender\">\n      <target>System.err</target>\n      <immediateFlush>true</immediateFlush>\n      <filter
      class=\"ch.qos.logback.classic.filter.ThresholdFilter\">\n          <level>WARN</level>\n      </filter>\n      <encoder>\n          <pattern>${pattern}</pattern>\n      </encoder>\n  </appender>\n\n  <root
      level=\"INFO\">\n      <appender-ref ref=\"STDOUT\" />\n      <appender-ref
      ref=\"STDERR\" />\n  </root>\n\n  <!-- Specific loggers -->\n  <logger name=\"akka\"
      level=\"INFO\"/>\n  <logger name=\"org.apache.kafka\" level=\"INFO\"/>\n  <logger
      name=\"org.apache.hadoop\" level=\"INFO\"/>\n  <logger name=\"org.apache.zookeeper\"
      level=\"INFO\"/>\n  <logger name=\"org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline\"
      level=\"OFF\"/>\n</configuration>\n"},"mode":"native"},"resource_metadata":{"apiVersion":"flink.apache.org/v1beta1","firstDeployment":false}}'
    lastStableSpec: '{"spec":{"job":{"jarURI":"local:///opt/flink/lib/flink-job-all.jar","parallelism":1,"entryClass":"org.ude.PassthroughJob","args":[],"state":"running","savepointTriggerNonce":null,"initialSavepointPath":null,"checkpointTriggerNonce":null,"upgradeMode":"stateless","allowNonRestoredState":null,"savepointRedeployNonce":null,"autoscalerResetNonce":null},"restartNonce":null,"flinkConfiguration":{"classloader.resolve-order":"parent-first","env.java.opts.all":"--add-opens=java.base/java.lang=ALL-UNNAMED
      --add-opens=java.base/java.util=ALL-UNNAMED","job.autoscaler.enabled":"true","job.autoscaler.memory.tuning.enabled":"true","job.autoscaler.memory.tuning.overhead":"1","job.autoscaler.metrics.window":"1m","job.autoscaler.scaling.enabled":"true","job.autoscaler.stabilization.interval":"1m","jobmanager.memory.process.size":"1GB","jobmanager.scheduler":"adaptive","kubernetes.container.image.pull-secrets":"mega-container-registry-secret","kubernetes.jobmanager.annotations":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","kubernetes.taskmanager.annotations":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","metrics.reporter.prom.factory.class":"org.apache.flink.metrics.prometheus.PrometheusReporterFactory","metrics.reporter.prom.port":"9249","rest.flamegraph.enabled":"true","rest.flamegraph.refresh-interval":"10
      m","rest.profiling.enabled":"true","taskmanager.memory.process.size":"3GB","taskmanager.numberOfTaskSlots":"5"},"image":"crmegahubwesteurope.azurecr.io/str-int/passthrough-job:1.0-SNAPSHOT","imagePullPolicy":null,"serviceAccount":"flink","flinkVersion":"v1_20","ingress":{"template":"str-int.apps.mega.cariad.cloud/{{name}}(/|$)(.*)","className":"nginx","annotations":{"nginx.ingress.kubernetes.io/auth-realm":"Authentication
      Required - Flink Playground","nginx.ingress.kubernetes.io/auth-secret":"ude-str-flink-basic-auth","nginx.ingress.kubernetes.io/auth-type":"basic","nginx.ingress.kubernetes.io/rewrite-target":"/$2"},"labels":null,"tls":null},"podTemplate":{"metadata":{"labels":{"app.kubernetes.io/name":"flink-passthrough-application-mode"}},"spec":{"containers":[{"env":[{"name":"env.java.opts.all","value":"--add-opens=java.base/java.lang=ALL-UNNAMED
      --add-opens=java.base/java.util=ALL-UNNAMED"},{"name":"BOOTSTRAP_SERVERS","value":"pkc-vr3yjz.westeurope.azure.confluent.cloud:9092"},{"name":"SCHEMA_REGISTRY_URL","value":"https://psrc-j39np.westeurope.azure.confluent.cloud"},{"name":"SCHEMA_REGISTRY_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_schema_registry_key","name":"ude-str-flink-gdc-job-schema-registry-secret"}}},{"name":"SCHEMA_REGISTRY_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_schema_registry_secret","name":"ude-str-flink-gdc-job-schema-registry-secret"}}},{"name":"KAFKA_CLUSTER_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_secret","name":"ude-str-flink-gdc-job-kafka-api-secret"}}},{"name":"KAFKA_CLUSTER_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_key","name":"ude-str-flink-gdc-job-kafka-api-secret"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"postgresql_admin_password","name":"ude-str-flink-gdc-job-db-password"}}},{"name":"KAFKA_PASSTHROUGH_SOURCE_TOPIC_NAME","value":"private.ude-str.passthrough.input.json"},{"name":"KAFKA_SINK_TOPIC_NAME","value":"public.ude-str.passthrough.output.json"},{"name":"FLINK_GROUP_ID","value":"flink-job-1"},{"name":"DB_URL","value":"******************************************************************************************"},{"name":"DB_USER_NAME","value":"psqladmin"},{"name":"DB_CLAIMS_DATABASE_NAME","value":"management-api"},{"name":"DB_PROCESSING_CLAIM_TABLE_NAME","value":"processing_claim"},{"name":"DB_VDC_DATABASE_NAME","value":"vdc"},{"name":"DB_VDC_RULE_TABLE_NAME","value":"normalization_rule"},{"name":"API_USE_CASE_ID","value":"passthrough"}],"name":"flink-main-container","securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"runAsNonRoot":true,"runAsUser":1000},"volumeMounts":[{"mountPath":"/opt/flink/log","name":"flink-logs"},{"mountPath":"/opt/flink/downloads","name":"downloads"}]}],"volumes":[{"emptyDir":{},"name":"flink-logs"},{"emptyDir":{},"name":"downloads"}]}},"jobManager":{"resource":{"cpu":1.0,"memory":"4Gi","ephemeralStorage":null},"replicas":1,"podTemplate":null},"taskManager":{"resource":{"cpu":0.5,"memory":"3221225472","ephemeralStorage":null},"replicas":null,"podTemplate":null},"logConfiguration":{"logback-console.xml":"<configuration>\n  <property
      name=\"pattern\" value=\"%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread)
      %cyan(%-12.36logger{36}) %msg%n\" />\n  <appender name=\"STDOUT\" class=\"ch.qos.logback.core.ConsoleAppender\">\n      <target>System.out</target>\n      <immediateFlush>true</immediateFlush>\n      <filter
      class=\"ch.qos.logback.classic.filter.LevelFilter\">\n          <level>ERROR</level>\n          <onMatch>DENY</onMatch>\n      </filter>\n      <filter
      class=\"ch.qos.logback.classic.filter.LevelFilter\">\n          <level>WARN</level>\n          <onMatch>DENY</onMatch>\n      </filter>\n      <encoder>\n          <pattern>${pattern}</pattern>\n      </encoder>\n  </appender>\n\n  <appender
      name=\"STDERR\" class=\"ch.qos.logback.core.ConsoleAppender\">\n      <target>System.err</target>\n      <immediateFlush>true</immediateFlush>\n      <filter
      class=\"ch.qos.logback.classic.filter.ThresholdFilter\">\n          <level>WARN</level>\n      </filter>\n      <encoder>\n          <pattern>${pattern}</pattern>\n      </encoder>\n  </appender>\n\n  <root
      level=\"INFO\">\n      <appender-ref ref=\"STDOUT\" />\n      <appender-ref
      ref=\"STDERR\" />\n  </root>\n\n  <!-- Specific loggers -->\n  <logger name=\"akka\"
      level=\"INFO\"/>\n  <logger name=\"org.apache.kafka\" level=\"INFO\"/>\n  <logger
      name=\"org.apache.hadoop\" level=\"INFO\"/>\n  <logger name=\"org.apache.zookeeper\"
      level=\"INFO\"/>\n  <logger name=\"org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline\"
      level=\"OFF\"/>\n</configuration>\n"},"mode":"native"},"resource_metadata":{"apiVersion":"flink.apache.org/v1beta1","firstDeployment":false}}'
    reconciliationTimestamp: 1746780131059
    state: DEPLOYED
  taskManager:
    labelSelector: component=taskmanager,app=flink-passthrough-application-mode
    replicas: 1
