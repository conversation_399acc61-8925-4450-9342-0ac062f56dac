apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  annotations:
    argocd.argoproj.io/compare-options: IgnoreExtraneous
    argocd.argoproj.io/hook: Skip
    argocd.argoproj.io/sync-options: Prune=false
  creationTimestamp: "2025-05-26T16:46:45Z"
  finalizers:
  - flinkdeployments.flink.apache.org/finalizer
  generation: 2
  labels:
    argocd.argoproj.io/instance: str-int_deployment-service
  name: gdc-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1
  namespace: str-int
  resourceVersion: "2948597865"
  uid: c5123794-c813-4db1-9479-46329c626d85
spec:
  flinkConfiguration:
    _ude.pipeline.dataOrderId: 65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1
    _ude.pipeline.name: streaming-pipeline-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1
    _ude.pipeline.projectId: a9c2c126-3f09-4435-a610-bef8210a634f
    _ude.pipeline.revision: "1"
    kubernetes.container.image.pull-secrets: mega-container-registry-secret
    *********************************: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/
    kubernetes.taskmanager.annotations: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/
    state.checkpoints.dir: UDE_REMOVED
    state.savepoints.dir: UDE_REMOVED
  flinkVersion: v1_20
  image: crmegahubwesteurope.azurecr.io/str-int/gdc-job:main
  imagePullPolicy: Always
  ingress:
    annotations:
      nginx.ingress.kubernetes.io/auth-realm: Authentication Required - Flink Playground
      nginx.ingress.kubernetes.io/auth-secret: deployment-service-flink-basic-auth
      nginx.ingress.kubernetes.io/auth-type: basic
      nginx.ingress.kubernetes.io/rewrite-target: /$2
    className: nginx
    template: str-int.apps.mega.cariad.cloud/{{name}}(/|$)(.*)
  job:
    args: []
    entryClass: org.ude.GdcTransformationJob
    jarURI: local:///opt/flink/lib/flink-job-all.jar
    parallelism: 1
    state: running
    upgradeMode: savepoint
  jobManager:
    replicas: 1
    resource:
      cpu: 1
      memory: 1Gi
  logConfiguration:
    logback-console.xml: |
      <configuration>
        <property name="pattern" value="%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread) %cyan(%-12.36logger{36}) %msg%n" />
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.out</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>ERROR</level>
                <onMatch>DENY</onMatch>
            </filter>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>WARN</level>
                <onMatch>DENY</onMatch>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <appender name="STDERR" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.err</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>WARN</level>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <root level="INFO">
            <appender-ref ref="STDOUT" />
            <appender-ref ref="STDERR" />
        </root>

        <!-- Specific loggers -->
        <logger name="akka" level="INFO"/>
        <logger name="org.apache.kafka" level="INFO"/>
        <logger name="org.apache.hadoop" level="INFO"/>
        <logger name="org.apache.zookeeper" level="INFO"/>
        <logger name="org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline" level="OFF"/>
      </configuration>
  mode: native
  podTemplate:
    metadata:
      labels:
        app.kubernetes.io/name: gdc-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1
        dataOrderId: 65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1
        jobType: gdc-job
        pipelineName: streaming-pipeline-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1
        projectId: a9c2c126-3f09-4435-a610-bef8210a634f
    spec:
      containers:
      - env:
        - name: SCHEMA_REGISTRY_API_KEY
          valueFrom:
            secretKeyRef:
              key: confluent_schema_registry_key
              name: deployment-service-gdc-job-schema-registry-secret
        - name: SCHEMA_REGISTRY_API_SECRET
          valueFrom:
            secretKeyRef:
              key: confluent_schema_registry_secret
              name: deployment-service-gdc-job-schema-registry-secret
        - name: KAFKA_CLUSTER_API_SECRET
          valueFrom:
            secretKeyRef:
              key: confluent_kafka_api_secret
              name: deployment-service-gdc-job-kafka-credentials
        - name: KAFKA_CLUSTER_API_KEY
          valueFrom:
            secretKeyRef:
              key: confluent_kafka_api_key
              name: deployment-service-gdc-job-kafka-credentials
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: postgresql_admin_password
              name: deployment-service-db-password
        - name: AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: azure_credential_client_secret
              name: deployment-service-store-and-explore-upload-spn-secret
        - name: AZURE_DOWNLOAD_CREDENTIAL_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: azure_credential_client_secret
              name: deployment-service-store-and-explore-download-spn-secret
        - name: FLINK_CONF_DIR
          value: /opt/flink/conf-ude
        - name: HADOOP_OPTIONAL_TOOLS
          value: hadoop-azure
        - name: BOOTSTRAP_SERVERS
          value: pkc-vr3yjz.westeurope.azure.confluent.cloud:9092
        - name: SCHEMA_REGISTRY_URL
          value: https://psrc-j39np.westeurope.azure.confluent.cloud
        - name: KAFKA_GDC_SOURCE_TOPIC_NAME
          value: private.ude-str.65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1.input.json
        - name: KAFKA_VMS_SOURCE_TOPIC_NAME
          value: private.ude-str.vms-connector.flattened.json
        - name: KAFKA_SINK_TOPIC_NAME
          value: public.ude-str.65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1.output.json
        - name: KAFKA_NON_RETRYABLE_DLQ_TOPIC_NAME
          value: private.ude-str.65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1.non-retryable.dlq.json
        - name: FLINK_GROUP_ID
          value: gdc-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1
        - name: DB_URL
          value: ***************************************************************************************************
        - name: DB_USER_NAME
          value: psqladmin
        - name: DB_CLAIMS_DATABASE_NAME
          value: management-api
        - name: DB_PROCESSING_CLAIM_TABLE_NAME
          value: processing_claim
        - name: DB_VDC_DATABASE_NAME
          value: signals
        - name: DB_VDC_RULES_TABLE_NAME
          value: normalization_rule
        - name: API_USE_CASE_ID
          value: 65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1
        - name: STORAGE_SCHEMA_ID
          value: 7b3ad26d-f90a-4370-acc6-bc8bb1b1dfa7
        - name: DATA_ORDER_RETENTION_DAYS
          value: "1"
        - name: ENRICHMENT_CONFIG
          value: '[{"schemaId":"7b3ad26d-f90a-4370-acc6-bc8bb1b1dfa7","enrichmentData":[{"source":"private.ude-str.vms-connector.flattened.json","type":"kafka","fields":[{"sourceName":"vin","outputName":"VIN"}]}]}]'
        - name: FCT_RULES
          value: '[{}]'
        envFrom:
        - configMapRef:
            name: deployment-service-store-and-explore-config
        - configMapRef:
            name: deployment-service-ude-flink-storage-config
        name: flink-main-container
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          runAsUser: 9999
        volumeMounts:
        - mountPath: /opt/flink/conf-ude
          name: ude-flink-conf-temp
        - mountPath: /opt/flink/log
          name: flink-logs
        - mountPath: /opt/flink/downloads
          name: downloads
      initContainers:
      - command:
        - /bin/sh
        - /opt/ude/scripts/init-flink.sh
        env:
        - name: FLINK_APPLICATION_NAME
          value: gdc-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1
        - name: AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: azure_credential_client_secret
              name: deployment-service-store-and-explore-upload-spn-secret
        - name: AZURE_UDE_CREDENTIAL_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: azure_credential_client_secret
              name: deployment-service-ude-flink-storage-spn-secret
              optional: true
        envFrom:
        - configMapRef:
            name: deployment-service-store-and-explore-config
        - configMapRef:
            name: deployment-service-ude-flink-storage-config
        image: busybox
        name: flink-init-container
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          runAsUser: 9999
        volumeMounts:
        - mountPath: /opt/ude/scripts
          name: ude-shell-scripts
        - mountPath: /tmp/ude/flink-custom-configs
          name: ude-flink-custom-configs
        - mountPath: /tmp/ude/flink-devops-tuning
          name: ude-flink-devops-tuning-configs
        - mountPath: /tmp/ude/flink-conf
          name: ude-flink-conf-temp
        - mountPath: /tmp/real-flink-config
          name: flink-config-volume
      volumes:
      - configMap:
          name: deployment-service-ude-shell-scripts
        name: ude-shell-scripts
      - configMap:
          name: deployment-service-flink-conf-yaml
        name: ude-flink-custom-configs
      - configMap:
          name: devops-tuning-gdc-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1
          optional: true
        name: ude-flink-devops-tuning-configs
      - emptyDir: {}
        name: ude-flink-conf-temp
      - emptyDir: {}
        name: flink-logs
      - emptyDir: {}
        name: downloads
  serviceAccount: deployment-service
  taskManager:
    resource:
      cpu: 6
      memory: 4Gi
status:
  clusterInfo:
    flink-revision: cb1e7b5 @ 2025-01-28T23:32:02+01:00
    flink-version: 1.20.1
    state-size: "92866"
    total-cpu: "7.0"
    total-memory: "**********"
  jobManagerDeploymentStatus: READY
  jobStatus:
    checkpointInfo:
      lastPeriodicCheckpointTimestamp: 0
    jobId: 2695a6262759d781e4aa44f56cd8dd6f
    jobName: GDC Transformation Job
    savepointInfo:
      lastPeriodicSavepointTimestamp: 0
      savepointHistory: []
    startTime: "*************"
    state: RUNNING
    updateTime: "*************"
  lifecycleState: STABLE
  observedGeneration: 2
  reconciliationStatus:
    lastReconciledSpec: '{"spec":{"job":{"jarURI":"local:///opt/flink/lib/flink-job-all.jar","parallelism":1,"entryClass":"org.ude.GdcTransformationJob","args":[],"state":"running","savepointTriggerNonce":null,"initialSavepointPath":null,"checkpointTriggerNonce":null,"upgradeMode":"stateless","allowNonRestoredState":null,"savepointRedeployNonce":null,"autoscalerResetNonce":null},"restartNonce":null,"flinkConfiguration":{"_ude.pipeline.dataOrderId":"65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1","_ude.pipeline.name":"streaming-pipeline-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1","_ude.pipeline.projectId":"a9c2c126-3f09-4435-a610-bef8210a634f","_ude.pipeline.revision":"1","kubernetes.container.image.pull-secrets":"mega-container-registry-secret","*********************************":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","kubernetes.taskmanager.annotations":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","state.checkpoints.dir":"UDE_REMOVED","state.savepoints.dir":"UDE_REMOVED"},"image":"crmegahubwesteurope.azurecr.io/str-int/gdc-job:main","imagePullPolicy":"Always","serviceAccount":"deployment-service","flinkVersion":"v1_20","ingress":{"template":"str-int.apps.mega.cariad.cloud/{{name}}(/|$)(.*)","className":"nginx","annotations":{"nginx.ingress.kubernetes.io/auth-realm":"Authentication
      Required - Flink Playground","nginx.ingress.kubernetes.io/auth-secret":"deployment-service-flink-basic-auth","nginx.ingress.kubernetes.io/auth-type":"basic","nginx.ingress.kubernetes.io/rewrite-target":"/$2"},"labels":null,"tls":null},"podTemplate":{"metadata":{"labels":{"app.kubernetes.io/name":"gdc-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1","dataOrderId":"65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1","jobType":"gdc-job","pipelineName":"streaming-pipeline-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1","projectId":"a9c2c126-3f09-4435-a610-bef8210a634f"}},"spec":{"containers":[{"env":[{"name":"SCHEMA_REGISTRY_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_schema_registry_key","name":"deployment-service-gdc-job-schema-registry-secret"}}},{"name":"SCHEMA_REGISTRY_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_schema_registry_secret","name":"deployment-service-gdc-job-schema-registry-secret"}}},{"name":"KAFKA_CLUSTER_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_secret","name":"deployment-service-gdc-job-kafka-credentials"}}},{"name":"KAFKA_CLUSTER_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_key","name":"deployment-service-gdc-job-kafka-credentials"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"postgresql_admin_password","name":"deployment-service-db-password"}}},{"name":"AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-store-and-explore-upload-spn-secret"}}},{"name":"AZURE_DOWNLOAD_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-store-and-explore-download-spn-secret"}}},{"name":"FLINK_CONF_DIR","value":"/opt/flink/conf-ude"},{"name":"HADOOP_OPTIONAL_TOOLS","value":"hadoop-azure"},{"name":"BOOTSTRAP_SERVERS","value":"pkc-vr3yjz.westeurope.azure.confluent.cloud:9092"},{"name":"SCHEMA_REGISTRY_URL","value":"https://psrc-j39np.westeurope.azure.confluent.cloud"},{"name":"KAFKA_GDC_SOURCE_TOPIC_NAME","value":"private.ude-str.65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1.input.json"},{"name":"KAFKA_VMS_SOURCE_TOPIC_NAME","value":"private.ude-str.vms-connector.flattened.json"},{"name":"KAFKA_SINK_TOPIC_NAME","value":"public.ude-str.65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1.output.json"},{"name":"KAFKA_NON_RETRYABLE_DLQ_TOPIC_NAME","value":"private.ude-str.65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1.non-retryable.dlq.json"},{"name":"FLINK_GROUP_ID","value":"gdc-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1"},{"name":"DB_URL","value":"***************************************************************************************************"},{"name":"DB_USER_NAME","value":"psqladmin"},{"name":"DB_CLAIMS_DATABASE_NAME","value":"management-api"},{"name":"DB_PROCESSING_CLAIM_TABLE_NAME","value":"processing_claim"},{"name":"DB_VDC_DATABASE_NAME","value":"signals"},{"name":"DB_VDC_RULES_TABLE_NAME","value":"normalization_rule"},{"name":"API_USE_CASE_ID","value":"65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1"},{"name":"STORAGE_SCHEMA_ID","value":"7b3ad26d-f90a-4370-acc6-bc8bb1b1dfa7"},{"name":"DATA_ORDER_RETENTION_DAYS","value":"1"},{"name":"ENRICHMENT_CONFIG","value":"[{\"schemaId\":\"7b3ad26d-f90a-4370-acc6-bc8bb1b1dfa7\",\"enrichmentData\":[{\"source\":\"private.ude-str.vms-connector.flattened.json\",\"type\":\"kafka\",\"fields\":[{\"sourceName\":\"vin\",\"outputName\":\"VIN\"}]}]}]"},{"name":"FCT_RULES","value":"[{}]"}],"envFrom":[{"configMapRef":{"name":"deployment-service-store-and-explore-config"}},{"configMapRef":{"name":"deployment-service-ude-flink-storage-config"}}],"name":"flink-main-container","securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"runAsNonRoot":true,"runAsUser":9999},"volumeMounts":[{"mountPath":"/opt/flink/conf-ude","name":"ude-flink-conf-temp"},{"mountPath":"/opt/flink/log","name":"flink-logs"},{"mountPath":"/opt/flink/downloads","name":"downloads"}]}],"initContainers":[{"command":["/bin/sh","/opt/ude/scripts/init-flink.sh"],"env":[{"name":"FLINK_APPLICATION_NAME","value":"gdc-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1"},{"name":"AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-store-and-explore-upload-spn-secret"}}},{"name":"AZURE_UDE_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-ude-flink-storage-spn-secret","optional":true}}}],"envFrom":[{"configMapRef":{"name":"deployment-service-store-and-explore-config"}},{"configMapRef":{"name":"deployment-service-ude-flink-storage-config"}}],"image":"busybox","name":"flink-init-container","securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"runAsNonRoot":true,"runAsUser":9999},"volumeMounts":[{"mountPath":"/opt/ude/scripts","name":"ude-shell-scripts"},{"mountPath":"/tmp/ude/flink-custom-configs","name":"ude-flink-custom-configs"},{"mountPath":"/tmp/ude/flink-devops-tuning","name":"ude-flink-devops-tuning-configs"},{"mountPath":"/tmp/ude/flink-conf","name":"ude-flink-conf-temp"},{"mountPath":"/tmp/real-flink-config","name":"flink-config-volume"}]}],"volumes":[{"configMap":{"name":"deployment-service-ude-shell-scripts"},"name":"ude-shell-scripts"},{"configMap":{"name":"deployment-service-flink-conf-yaml"},"name":"ude-flink-custom-configs"},{"configMap":{"name":"devops-tuning-gdc-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1","optional":true},"name":"ude-flink-devops-tuning-configs"},{"emptyDir":{},"name":"ude-flink-conf-temp"},{"emptyDir":{},"name":"flink-logs"},{"emptyDir":{},"name":"downloads"}]}},"jobManager":{"resource":{"cpu":1.0,"memory":"1Gi","ephemeralStorage":null},"replicas":1,"podTemplate":null},"taskManager":{"resource":{"cpu":6.0,"memory":"4Gi","ephemeralStorage":null},"replicas":null,"podTemplate":null},"logConfiguration":{"logback-console.xml":"<configuration>\n  <property
      name=\"pattern\" value=\"%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread)
      %cyan(%-12.36logger{36}) %msg%n\" />\n  <appender name=\"STDOUT\" class=\"ch.qos.logback.core.ConsoleAppender\">\n      <target>System.out</target>\n      <immediateFlush>true</immediateFlush>\n      <filter
      class=\"ch.qos.logback.classic.filter.LevelFilter\">\n          <level>ERROR</level>\n          <onMatch>DENY</onMatch>\n      </filter>\n      <filter
      class=\"ch.qos.logback.classic.filter.LevelFilter\">\n          <level>WARN</level>\n          <onMatch>DENY</onMatch>\n      </filter>\n      <encoder>\n          <pattern>${pattern}</pattern>\n      </encoder>\n  </appender>\n\n  <appender
      name=\"STDERR\" class=\"ch.qos.logback.core.ConsoleAppender\">\n      <target>System.err</target>\n      <immediateFlush>true</immediateFlush>\n      <filter
      class=\"ch.qos.logback.classic.filter.ThresholdFilter\">\n          <level>WARN</level>\n      </filter>\n      <encoder>\n          <pattern>${pattern}</pattern>\n      </encoder>\n  </appender>\n\n  <root
      level=\"INFO\">\n      <appender-ref ref=\"STDOUT\" />\n      <appender-ref
      ref=\"STDERR\" />\n  </root>\n\n  <!-- Specific loggers -->\n  <logger name=\"akka\"
      level=\"INFO\"/>\n  <logger name=\"org.apache.kafka\" level=\"INFO\"/>\n  <logger
      name=\"org.apache.hadoop\" level=\"INFO\"/>\n  <logger name=\"org.apache.zookeeper\"
      level=\"INFO\"/>\n  <logger name=\"org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline\"
      level=\"OFF\"/>\n</configuration>\n"},"mode":"native"},"resource_metadata":{"apiVersion":"flink.apache.org/v1beta1","firstDeployment":true}}'
    lastStableSpec: '{"spec":{"job":{"jarURI":"local:///opt/flink/lib/flink-job-all.jar","parallelism":1,"entryClass":"org.ude.GdcTransformationJob","args":[],"state":"running","savepointTriggerNonce":null,"initialSavepointPath":null,"checkpointTriggerNonce":null,"upgradeMode":"stateless","allowNonRestoredState":null,"savepointRedeployNonce":null,"autoscalerResetNonce":null},"restartNonce":null,"flinkConfiguration":{"_ude.pipeline.dataOrderId":"65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1","_ude.pipeline.name":"streaming-pipeline-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1","_ude.pipeline.projectId":"a9c2c126-3f09-4435-a610-bef8210a634f","_ude.pipeline.revision":"1","kubernetes.container.image.pull-secrets":"mega-container-registry-secret","*********************************":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","kubernetes.taskmanager.annotations":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","state.checkpoints.dir":"UDE_REMOVED","state.savepoints.dir":"UDE_REMOVED"},"image":"crmegahubwesteurope.azurecr.io/str-int/gdc-job:main","imagePullPolicy":"Always","serviceAccount":"deployment-service","flinkVersion":"v1_20","ingress":{"template":"str-int.apps.mega.cariad.cloud/{{name}}(/|$)(.*)","className":"nginx","annotations":{"nginx.ingress.kubernetes.io/auth-realm":"Authentication
      Required - Flink Playground","nginx.ingress.kubernetes.io/auth-secret":"deployment-service-flink-basic-auth","nginx.ingress.kubernetes.io/auth-type":"basic","nginx.ingress.kubernetes.io/rewrite-target":"/$2"},"labels":null,"tls":null},"podTemplate":{"metadata":{"labels":{"app.kubernetes.io/name":"gdc-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1","dataOrderId":"65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1","jobType":"gdc-job","pipelineName":"streaming-pipeline-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1","projectId":"a9c2c126-3f09-4435-a610-bef8210a634f"}},"spec":{"containers":[{"env":[{"name":"SCHEMA_REGISTRY_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_schema_registry_key","name":"deployment-service-gdc-job-schema-registry-secret"}}},{"name":"SCHEMA_REGISTRY_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_schema_registry_secret","name":"deployment-service-gdc-job-schema-registry-secret"}}},{"name":"KAFKA_CLUSTER_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_secret","name":"deployment-service-gdc-job-kafka-credentials"}}},{"name":"KAFKA_CLUSTER_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_key","name":"deployment-service-gdc-job-kafka-credentials"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"postgresql_admin_password","name":"deployment-service-db-password"}}},{"name":"AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-store-and-explore-upload-spn-secret"}}},{"name":"AZURE_DOWNLOAD_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-store-and-explore-download-spn-secret"}}},{"name":"FLINK_CONF_DIR","value":"/opt/flink/conf-ude"},{"name":"HADOOP_OPTIONAL_TOOLS","value":"hadoop-azure"},{"name":"BOOTSTRAP_SERVERS","value":"pkc-vr3yjz.westeurope.azure.confluent.cloud:9092"},{"name":"SCHEMA_REGISTRY_URL","value":"https://psrc-j39np.westeurope.azure.confluent.cloud"},{"name":"KAFKA_GDC_SOURCE_TOPIC_NAME","value":"private.ude-str.65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1.input.json"},{"name":"KAFKA_VMS_SOURCE_TOPIC_NAME","value":"private.ude-str.vms-connector.flattened.json"},{"name":"KAFKA_SINK_TOPIC_NAME","value":"public.ude-str.65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1.output.json"},{"name":"KAFKA_NON_RETRYABLE_DLQ_TOPIC_NAME","value":"private.ude-str.65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1.non-retryable.dlq.json"},{"name":"FLINK_GROUP_ID","value":"gdc-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1"},{"name":"DB_URL","value":"***************************************************************************************************"},{"name":"DB_USER_NAME","value":"psqladmin"},{"name":"DB_CLAIMS_DATABASE_NAME","value":"management-api"},{"name":"DB_PROCESSING_CLAIM_TABLE_NAME","value":"processing_claim"},{"name":"DB_VDC_DATABASE_NAME","value":"signals"},{"name":"DB_VDC_RULES_TABLE_NAME","value":"normalization_rule"},{"name":"API_USE_CASE_ID","value":"65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1"},{"name":"STORAGE_SCHEMA_ID","value":"7b3ad26d-f90a-4370-acc6-bc8bb1b1dfa7"},{"name":"DATA_ORDER_RETENTION_DAYS","value":"1"},{"name":"ENRICHMENT_CONFIG","value":"[{\"schemaId\":\"7b3ad26d-f90a-4370-acc6-bc8bb1b1dfa7\",\"enrichmentData\":[{\"source\":\"private.ude-str.vms-connector.flattened.json\",\"type\":\"kafka\",\"fields\":[{\"sourceName\":\"vin\",\"outputName\":\"VIN\"}]}]}]"},{"name":"FCT_RULES","value":"[{}]"}],"envFrom":[{"configMapRef":{"name":"deployment-service-store-and-explore-config"}},{"configMapRef":{"name":"deployment-service-ude-flink-storage-config"}}],"name":"flink-main-container","securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"runAsNonRoot":true,"runAsUser":9999},"volumeMounts":[{"mountPath":"/opt/flink/conf-ude","name":"ude-flink-conf-temp"},{"mountPath":"/opt/flink/log","name":"flink-logs"},{"mountPath":"/opt/flink/downloads","name":"downloads"}]}],"initContainers":[{"command":["/bin/sh","/opt/ude/scripts/init-flink.sh"],"env":[{"name":"FLINK_APPLICATION_NAME","value":"gdc-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1"},{"name":"AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-store-and-explore-upload-spn-secret"}}},{"name":"AZURE_UDE_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-ude-flink-storage-spn-secret","optional":true}}}],"envFrom":[{"configMapRef":{"name":"deployment-service-store-and-explore-config"}},{"configMapRef":{"name":"deployment-service-ude-flink-storage-config"}}],"image":"busybox","name":"flink-init-container","securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"runAsNonRoot":true,"runAsUser":9999},"volumeMounts":[{"mountPath":"/opt/ude/scripts","name":"ude-shell-scripts"},{"mountPath":"/tmp/ude/flink-custom-configs","name":"ude-flink-custom-configs"},{"mountPath":"/tmp/ude/flink-devops-tuning","name":"ude-flink-devops-tuning-configs"},{"mountPath":"/tmp/ude/flink-conf","name":"ude-flink-conf-temp"},{"mountPath":"/tmp/real-flink-config","name":"flink-config-volume"}]}],"volumes":[{"configMap":{"name":"deployment-service-ude-shell-scripts"},"name":"ude-shell-scripts"},{"configMap":{"name":"deployment-service-flink-conf-yaml"},"name":"ude-flink-custom-configs"},{"configMap":{"name":"devops-tuning-gdc-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1","optional":true},"name":"ude-flink-devops-tuning-configs"},{"emptyDir":{},"name":"ude-flink-conf-temp"},{"emptyDir":{},"name":"flink-logs"},{"emptyDir":{},"name":"downloads"}]}},"jobManager":{"resource":{"cpu":1.0,"memory":"1Gi","ephemeralStorage":null},"replicas":1,"podTemplate":null},"taskManager":{"resource":{"cpu":6.0,"memory":"4Gi","ephemeralStorage":null},"replicas":null,"podTemplate":null},"logConfiguration":{"logback-console.xml":"<configuration>\n  <property
      name=\"pattern\" value=\"%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread)
      %cyan(%-12.36logger{36}) %msg%n\" />\n  <appender name=\"STDOUT\" class=\"ch.qos.logback.core.ConsoleAppender\">\n      <target>System.out</target>\n      <immediateFlush>true</immediateFlush>\n      <filter
      class=\"ch.qos.logback.classic.filter.LevelFilter\">\n          <level>ERROR</level>\n          <onMatch>DENY</onMatch>\n      </filter>\n      <filter
      class=\"ch.qos.logback.classic.filter.LevelFilter\">\n          <level>WARN</level>\n          <onMatch>DENY</onMatch>\n      </filter>\n      <encoder>\n          <pattern>${pattern}</pattern>\n      </encoder>\n  </appender>\n\n  <appender
      name=\"STDERR\" class=\"ch.qos.logback.core.ConsoleAppender\">\n      <target>System.err</target>\n      <immediateFlush>true</immediateFlush>\n      <filter
      class=\"ch.qos.logback.classic.filter.ThresholdFilter\">\n          <level>WARN</level>\n      </filter>\n      <encoder>\n          <pattern>${pattern}</pattern>\n      </encoder>\n  </appender>\n\n  <root
      level=\"INFO\">\n      <appender-ref ref=\"STDOUT\" />\n      <appender-ref
      ref=\"STDERR\" />\n  </root>\n\n  <!-- Specific loggers -->\n  <logger name=\"akka\"
      level=\"INFO\"/>\n  <logger name=\"org.apache.kafka\" level=\"INFO\"/>\n  <logger
      name=\"org.apache.hadoop\" level=\"INFO\"/>\n  <logger name=\"org.apache.zookeeper\"
      level=\"INFO\"/>\n  <logger name=\"org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline\"
      level=\"OFF\"/>\n</configuration>\n"},"mode":"native"},"resource_metadata":{"apiVersion":"flink.apache.org/v1beta1","firstDeployment":true}}'
    reconciliationTimestamp: 1748278006746
    state: DEPLOYED
  taskManager:
    labelSelector: component=taskmanager,app=gdc-65dbbce9-ab9b-4e33-b5c3-d1d538ba45b1
    replicas: 1
