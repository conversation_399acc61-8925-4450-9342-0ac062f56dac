apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  annotations:
    argocd.argoproj.io/compare-options: IgnoreExtraneous
    argocd.argoproj.io/hook: Skip
    argocd.argoproj.io/sync-options: Prune=false
  creationTimestamp: "2025-06-04T10:52:10Z"
  finalizers:
  - flinkdeployments.flink.apache.org/finalizer
  generation: 2
  labels:
    argocd.argoproj.io/instance: str-int_deployment-service
  name: pthru-388b532c-b0b4-4fde-9ddc-faec810ae82b
  namespace: str-int
  resourceVersion: "2965163845"
  uid: 5700ed18-e56a-400a-a9ff-6407791ede70
spec:
  flinkConfiguration:
    classloader.resolve-order: parent-first
    env.java.opts.all: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED
      -XX:+UseParallelGC -XX:ParallelGCThreads=2 -Xmx2000m -Xms1048m -XX:SurvivorRatio=3
      -XX:GCTimeRatio=2 -XX:MaxGCPauseMillis=6000 -XX:ConcGCThreads=2 -XX:+UseStringDeduplication
    job.autoscaler.enabled: "false"
    job.autoscaler.memory.tuning.enabled: "false"
    job.autoscaler.memory.tuning.overhead: "1"
    job.autoscaler.metrics.window: 1m
    job.autoscaler.scaling.enabled: "false"
    job.autoscaler.stabilization.interval: 1m
    jobmanager.memory.process.size: 1GB
    jobmanager.scheduler: adaptive
    kubernetes.container.image.pull-secrets: mega-container-registry-secret
    *********************************: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/
    kubernetes.taskmanager.annotations: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/
    metrics.reporter.prom.factory.class: org.apache.flink.metrics.prometheus.PrometheusReporterFactory
    metrics.reporter.prom.port: "9249"
    rest.flamegraph.enabled: "true"
    rest.flamegraph.refresh-interval: 10 m
    rest.profiling.enabled: "true"
    taskmanager.memory.process.size: 5GB
    taskmanager.numberOfTaskSlots: "18"
  flinkVersion: v1_20
  image: crmegahubwesteurope.azurecr.io/str-int/passthrough-job:main
  imagePullPolicy: Always
  ingress:
    annotations:
      nginx.ingress.kubernetes.io/auth-realm: Authentication Required - Flink Playground
      nginx.ingress.kubernetes.io/auth-secret: deployment-service-flink-basic-auth
      nginx.ingress.kubernetes.io/auth-type: basic
      nginx.ingress.kubernetes.io/rewrite-target: /$2
    className: nginx
    template: str-int.apps.mega.cariad.cloud/{{name}}(/|$)(.*)
  job:
    args: []
    entryClass: org.ude.PassthroughJob
    jarURI: local:///opt/flink/lib/flink-job-all.jar
    parallelism: 16
    state: running
    upgradeMode: stateless
  jobManager:
    replicas: 1
    resource:
      cpu: 1
      memory: 1Gi
  logConfiguration:
    logback-console.xml: |
      <configuration>
        <property name="pattern" value="%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread) %cyan(%-12.36logger{36}) %msg%n" />
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.out</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>ERROR</level>
                <onMatch>DENY</onMatch>
            </filter>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>WARN</level>
                <onMatch>DENY</onMatch>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <appender name="STDERR" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.err</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>WARN</level>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <root level="INFO">
            <appender-ref ref="STDOUT" />
            <appender-ref ref="STDERR" />
        </root>

        <!-- Specific loggers -->
        <logger name="akka" level="INFO"/>
        <logger name="org.apache.kafka" level="INFO"/>
        <logger name="org.apache.hadoop" level="INFO"/>
        <logger name="org.apache.zookeeper" level="INFO"/>
        <logger name="org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline" level="OFF"/>
      </configuration>
  mode: native
  podTemplate:
    metadata:
      labels:
        app.kubernetes.io/name: pthru-388b532c-b0b4-4fde-9ddc-faec810ae82b
        dataOrderId: 388b532c-b0b4-4fde-9ddc-faec810ae82b
        jobType: passthrough-job
        pipelineName: StreamingPipeline.VW.testPI
        projectId: b8c5f788-fc04-427d-86e7-b30f77cadf7b
    spec:
      containers:
      - env:
        - name: BOOTSTRAP_SERVERS
          value: pkc-vr3yjz.westeurope.azure.confluent.cloud:9092
        - name: SCHEMA_REGISTRY_URL
          value: https://psrc-j39np.westeurope.azure.confluent.cloud
        - name: SCHEMA_REGISTRY_API_KEY
          valueFrom:
            secretKeyRef:
              key: confluent_schema_registry_key
              name: deployment-service-gdc-job-schema-registry-secret
        - name: SCHEMA_REGISTRY_API_SECRET
          valueFrom:
            secretKeyRef:
              key: confluent_schema_registry_secret
              name: deployment-service-gdc-job-schema-registry-secret
        - name: KAFKA_CLUSTER_API_SECRET
          valueFrom:
            secretKeyRef:
              key: confluent_kafka_api_secret
              name: deployment-service-gdc-job-kafka-credentials
        - name: KAFKA_CLUSTER_API_KEY
          valueFrom:
            secretKeyRef:
              key: confluent_kafka_api_key
              name: deployment-service-gdc-job-kafka-credentials
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: postgresql_admin_password
              name: deployment-service-db-password
        - name: AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: azure_credential_client_secret
              name: deployment-service-store-and-explore-upload-spn-secret
        - name: AZURE_DOWNLOAD_CREDENTIAL_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: azure_credential_client_secret
              name: deployment-service-store-and-explore-download-spn-secret
        - name: FLINK_CONF_DIR
          value: /opt/flink/conf-ude
        - name: KAFKA_PASSTHROUGH_SOURCE_TOPIC_NAME
          value: private.ude-str.388b532c-b0b4-4fde-9ddc-faec810ae82b.input.json
        - name: KAFKA_SINK_TOPIC_NAME
          value: public.ude-str.388b532c-b0b4-4fde-9ddc-faec810ae82b.output.json
        - name: KAFKA_NON_RETRYABLE_DLQ_TOPIC_NAME
          value: private.ude-str.388b532c-b0b4-4fde-9ddc-faec810ae82b.non-retryable.dlq.json
        - name: FLINK_GROUP_ID
          value: pthru-388b532c-b0b4-4fde-9ddc-faec810ae82b
        - name: DB_URL
          value: ***************************************************************************************************
        - name: DB_USER_NAME
          value: psqladmin
        - name: DB_CLAIMS_DATABASE_NAME
          value: management-api
        - name: DB_PROCESSING_CLAIM_TABLE_NAME
          value: processing_claim
        - name: API_USE_CASE_ID
          value: 388b532c-b0b4-4fde-9ddc-faec810ae82b
        - name: DATA_ORDER_RETENTION_DAYS
          value: "7"
        envFrom:
        - configMapRef:
            name: deployment-service-store-and-explore-config
        - configMapRef:
            name: deployment-service-ude-flink-storage-config
        name: flink-main-container
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          runAsUser: 9999
        volumeMounts:
        - mountPath: /opt/flink/conf-ude
          name: ude-flink-conf-temp
        - mountPath: /opt/flink/log
          name: flink-logs
        - mountPath: /opt/flink/downloads
          name: downloads
      initContainers:
      - command:
        - /bin/sh
        - /opt/ude/scripts/init-flink.sh
        env:
        - name: FLINK_APPLICATION_NAME
          value: pthru-388b532c-b0b4-4fde-9ddc-faec810ae82b
        - name: AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: azure_credential_client_secret
              name: deployment-service-store-and-explore-upload-spn-secret
        - name: AZURE_UDE_CREDENTIAL_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: azure_credential_client_secret
              name: deployment-service-ude-flink-storage-spn-secret
              optional: true
        envFrom:
        - configMapRef:
            name: deployment-service-store-and-explore-config
        - configMapRef:
            name: deployment-service-ude-flink-storage-config
        image: busybox
        name: flink-init-container
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          runAsUser: 9999
        volumeMounts:
        - mountPath: /opt/ude/scripts
          name: ude-shell-scripts
        - mountPath: /tmp/ude/flink-custom-configs
          name: ude-flink-custom-configs
        - mountPath: /tmp/ude/flink-devops-tuning
          name: ude-flink-devops-tuning-configs
        - mountPath: /tmp/ude/flink-conf
          name: ude-flink-conf-temp
        - mountPath: /tmp/real-flink-config
          name: flink-config-volume
      volumes:
      - configMap:
          name: deployment-service-ude-shell-scripts
        name: ude-shell-scripts
      - configMap:
          name: deployment-service-flink-conf-yaml
        name: ude-flink-custom-configs
      - configMap:
          name: devops-tuning-pthru-388b532c-b0b4-4fde-9ddc-faec810ae82b
          optional: true
        name: ude-flink-devops-tuning-configs
      - emptyDir: {}
        name: ude-flink-conf-temp
      - emptyDir: {}
        name: flink-logs
      - emptyDir: {}
        name: downloads
  serviceAccount: deployment-service
  taskManager:
    resource:
      cpu: 5
      memory: 5Gi
status:
  clusterInfo: {}
  error: '{"type":"org.apache.flink.kubernetes.operator.exception.ReconciliationException","message":"org.apache.flink.kubernetes.operator.exception.ReconciliationException:
    Could not observe latest savepoint information","additionalMetadata":{},"throwableList":[{"type":"org.apache.flink.kubernetes.operator.exception.ReconciliationException","message":"Could
    not observe latest savepoint information","additionalMetadata":{}},{"type":"java.util.concurrent.ExecutionException","message":"org.apache.flink.runtime.rest.util.RestClientException:
    [org.apache.flink.runtime.rest.handler.RestHandlerException: Checkpointing has
    not been enabled.\n\tat org.apache.flink.runtime.rest.handler.job.checkpoints.CheckpointingStatisticsHandler.createCheckpointingStatistics(CheckpointingStatisticsHandler.java:107)\n\tat
    org.apache.flink.runtime.rest.handler.job.checkpoints.CheckpointingStatisticsHandler.handleCheckpointStatsRequest(CheckpointingStatisticsHandler.java:85)\n\tat
    org.apache.flink.runtime.rest.handler.job.checkpoints.CheckpointingStatisticsHandler.handleCheckpointStatsRequest(CheckpointingStatisticsHandler.java:59)\n\tat
    org.apache.flink.runtime.rest.handler.job.checkpoints.AbstractCheckpointStatsHandler.lambda$handleRequest$1(AbstractCheckpointStatsHandler.java:89)\n\tat
    java.base/java.util.concurrent.CompletableFuture$UniApply.tryFire(Unknown Source)\n\tat
    java.base/java.util.concurrent.CompletableFuture$Completion.run(Unknown Source)\n\tat
    java.base/java.util.concurrent.Executors$RunnableAdapter.call(Unknown Source)\n\tat
    java.base/java.util.concurrent.FutureTask.run(Unknown Source)\n\tat java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(Unknown
    Source)\n\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown
    Source)\n\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown
    Source)\n\tat java.base/java.lang.Thread.run(Unknown Source)\n]","additionalMetadata":{}}]}'
  jobManagerDeploymentStatus: DEPLOYED_NOT_READY
  jobStatus:
    checkpointInfo:
      lastPeriodicCheckpointTimestamp: 0
    jobId: 44f6d4be62e09aa56e4f33f6828ee77a
    savepointInfo:
      lastPeriodicSavepointTimestamp: 0
      savepointHistory: []
    state: RECONCILING
  lifecycleState: DEPLOYED
  observedGeneration: 2
  reconciliationStatus:
    lastReconciledSpec: '{"spec":{"job":{"jarURI":"local:///opt/flink/lib/flink-job-all.jar","parallelism":16,"entryClass":"org.ude.PassthroughJob","args":[],"state":"running","savepointTriggerNonce":null,"initialSavepointPath":null,"checkpointTriggerNonce":null,"upgradeMode":"stateless","allowNonRestoredState":null,"savepointRedeployNonce":null,"autoscalerResetNonce":null},"restartNonce":null,"flinkConfiguration":{"classloader.resolve-order":"parent-first","env.java.opts.all":"--add-opens=java.base/java.lang=ALL-UNNAMED
      --add-opens=java.base/java.util=ALL-UNNAMED -XX:+UseParallelGC -XX:ParallelGCThreads=2
      -Xmx2000m -Xms1048m -XX:SurvivorRatio=3 -XX:GCTimeRatio=2 -XX:MaxGCPauseMillis=6000
      -XX:ConcGCThreads=2 -XX:+UseStringDeduplication","job.autoscaler.enabled":"false","job.autoscaler.memory.tuning.enabled":"false","job.autoscaler.memory.tuning.overhead":"1","job.autoscaler.metrics.window":"1m","job.autoscaler.scaling.enabled":"false","job.autoscaler.stabilization.interval":"1m","jobmanager.memory.process.size":"1GB","jobmanager.scheduler":"adaptive","kubernetes.container.image.pull-secrets":"mega-container-registry-secret","*********************************":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","kubernetes.taskmanager.annotations":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","metrics.reporter.prom.factory.class":"org.apache.flink.metrics.prometheus.PrometheusReporterFactory","metrics.reporter.prom.port":"9249","rest.flamegraph.enabled":"true","rest.flamegraph.refresh-interval":"10
      m","rest.profiling.enabled":"true","taskmanager.memory.process.size":"5GB","taskmanager.numberOfTaskSlots":"18"},"image":"crmegahubwesteurope.azurecr.io/str-int/passthrough-job:main","imagePullPolicy":"Always","serviceAccount":"deployment-service","flinkVersion":"v1_20","ingress":{"template":"str-int.apps.mega.cariad.cloud/{{name}}(/|$)(.*)","className":"nginx","annotations":{"nginx.ingress.kubernetes.io/auth-realm":"Authentication
      Required - Flink Playground","nginx.ingress.kubernetes.io/auth-secret":"deployment-service-flink-basic-auth","nginx.ingress.kubernetes.io/auth-type":"basic","nginx.ingress.kubernetes.io/rewrite-target":"/$2"},"labels":null,"tls":null},"podTemplate":{"metadata":{"labels":{"app.kubernetes.io/name":"pthru-388b532c-b0b4-4fde-9ddc-faec810ae82b","dataOrderId":"388b532c-b0b4-4fde-9ddc-faec810ae82b","jobType":"passthrough-job","pipelineName":"StreamingPipeline.VW.testPI","projectId":"b8c5f788-fc04-427d-86e7-b30f77cadf7b"}},"spec":{"containers":[{"env":[{"name":"BOOTSTRAP_SERVERS","value":"pkc-vr3yjz.westeurope.azure.confluent.cloud:9092"},{"name":"SCHEMA_REGISTRY_URL","value":"https://psrc-j39np.westeurope.azure.confluent.cloud"},{"name":"SCHEMA_REGISTRY_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_schema_registry_key","name":"deployment-service-gdc-job-schema-registry-secret"}}},{"name":"SCHEMA_REGISTRY_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_schema_registry_secret","name":"deployment-service-gdc-job-schema-registry-secret"}}},{"name":"KAFKA_CLUSTER_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_secret","name":"deployment-service-gdc-job-kafka-credentials"}}},{"name":"KAFKA_CLUSTER_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_key","name":"deployment-service-gdc-job-kafka-credentials"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"postgresql_admin_password","name":"deployment-service-db-password"}}},{"name":"AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-store-and-explore-upload-spn-secret"}}},{"name":"AZURE_DOWNLOAD_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-store-and-explore-download-spn-secret"}}},{"name":"FLINK_CONF_DIR","value":"/opt/flink/conf-ude"},{"name":"KAFKA_PASSTHROUGH_SOURCE_TOPIC_NAME","value":"private.ude-str.388b532c-b0b4-4fde-9ddc-faec810ae82b.input.json"},{"name":"KAFKA_SINK_TOPIC_NAME","value":"public.ude-str.388b532c-b0b4-4fde-9ddc-faec810ae82b.output.json"},{"name":"KAFKA_NON_RETRYABLE_DLQ_TOPIC_NAME","value":"private.ude-str.388b532c-b0b4-4fde-9ddc-faec810ae82b.non-retryable.dlq.json"},{"name":"FLINK_GROUP_ID","value":"pthru-388b532c-b0b4-4fde-9ddc-faec810ae82b"},{"name":"DB_URL","value":"***************************************************************************************************"},{"name":"DB_USER_NAME","value":"psqladmin"},{"name":"DB_CLAIMS_DATABASE_NAME","value":"management-api"},{"name":"DB_PROCESSING_CLAIM_TABLE_NAME","value":"processing_claim"},{"name":"API_USE_CASE_ID","value":"388b532c-b0b4-4fde-9ddc-faec810ae82b"},{"name":"DATA_ORDER_RETENTION_DAYS","value":"7"}],"envFrom":[{"configMapRef":{"name":"deployment-service-store-and-explore-config"}},{"configMapRef":{"name":"deployment-service-ude-flink-storage-config"}}],"name":"flink-main-container","securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"runAsNonRoot":true,"runAsUser":9999},"volumeMounts":[{"mountPath":"/opt/flink/conf-ude","name":"ude-flink-conf-temp"},{"mountPath":"/opt/flink/log","name":"flink-logs"},{"mountPath":"/opt/flink/downloads","name":"downloads"}]}],"initContainers":[{"command":["/bin/sh","/opt/ude/scripts/init-flink.sh"],"env":[{"name":"FLINK_APPLICATION_NAME","value":"pthru-388b532c-b0b4-4fde-9ddc-faec810ae82b"},{"name":"AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-store-and-explore-upload-spn-secret"}}},{"name":"AZURE_UDE_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"deployment-service-ude-flink-storage-spn-secret","optional":true}}}],"envFrom":[{"configMapRef":{"name":"deployment-service-store-and-explore-config"}},{"configMapRef":{"name":"deployment-service-ude-flink-storage-config"}}],"image":"busybox","name":"flink-init-container","securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"runAsNonRoot":true,"runAsUser":9999},"volumeMounts":[{"mountPath":"/opt/ude/scripts","name":"ude-shell-scripts"},{"mountPath":"/tmp/ude/flink-custom-configs","name":"ude-flink-custom-configs"},{"mountPath":"/tmp/ude/flink-devops-tuning","name":"ude-flink-devops-tuning-configs"},{"mountPath":"/tmp/ude/flink-conf","name":"ude-flink-conf-temp"},{"mountPath":"/tmp/real-flink-config","name":"flink-config-volume"}]}],"volumes":[{"configMap":{"name":"deployment-service-ude-shell-scripts"},"name":"ude-shell-scripts"},{"configMap":{"name":"deployment-service-flink-conf-yaml"},"name":"ude-flink-custom-configs"},{"configMap":{"name":"devops-tuning-pthru-388b532c-b0b4-4fde-9ddc-faec810ae82b","optional":true},"name":"ude-flink-devops-tuning-configs"},{"emptyDir":{},"name":"ude-flink-conf-temp"},{"emptyDir":{},"name":"flink-logs"},{"emptyDir":{},"name":"downloads"}]}},"jobManager":{"resource":{"cpu":1.0,"memory":"1Gi","ephemeralStorage":null},"replicas":1,"podTemplate":null},"taskManager":{"resource":{"cpu":5.0,"memory":"5Gi","ephemeralStorage":null},"replicas":null,"podTemplate":null},"logConfiguration":{"logback-console.xml":"<configuration>\n  <property
      name=\"pattern\" value=\"%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread)
      %cyan(%-12.36logger{36}) %msg%n\" />\n  <appender name=\"STDOUT\" class=\"ch.qos.logback.core.ConsoleAppender\">\n      <target>System.out</target>\n      <immediateFlush>true</immediateFlush>\n      <filter
      class=\"ch.qos.logback.classic.filter.LevelFilter\">\n          <level>ERROR</level>\n          <onMatch>DENY</onMatch>\n      </filter>\n      <filter
      class=\"ch.qos.logback.classic.filter.LevelFilter\">\n          <level>WARN</level>\n          <onMatch>DENY</onMatch>\n      </filter>\n      <encoder>\n          <pattern>${pattern}</pattern>\n      </encoder>\n  </appender>\n\n  <appender
      name=\"STDERR\" class=\"ch.qos.logback.core.ConsoleAppender\">\n      <target>System.err</target>\n      <immediateFlush>true</immediateFlush>\n      <filter
      class=\"ch.qos.logback.classic.filter.ThresholdFilter\">\n          <level>WARN</level>\n      </filter>\n      <encoder>\n          <pattern>${pattern}</pattern>\n      </encoder>\n  </appender>\n\n  <root
      level=\"INFO\">\n      <appender-ref ref=\"STDOUT\" />\n      <appender-ref
      ref=\"STDERR\" />\n  </root>\n\n  <!-- Specific loggers -->\n  <logger name=\"akka\"
      level=\"INFO\"/>\n  <logger name=\"org.apache.kafka\" level=\"INFO\"/>\n  <logger
      name=\"org.apache.hadoop\" level=\"INFO\"/>\n  <logger name=\"org.apache.zookeeper\"
      level=\"INFO\"/>\n  <logger name=\"org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline\"
      level=\"OFF\"/>\n</configuration>\n"},"mode":"native"},"resource_metadata":{"apiVersion":"flink.apache.org/v1beta1","firstDeployment":true}}'
    reconciliationTimestamp: 1749034331417
    state: DEPLOYED
  taskManager:
    labelSelector: component=taskmanager,app=pthru-388b532c-b0b4-4fde-9ddc-faec810ae82b
    replicas: 1
