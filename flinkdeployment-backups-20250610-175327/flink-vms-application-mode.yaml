apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  annotations:
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"flink.apache.org/v1beta1","kind":"FlinkDeployment","metadata":{"annotations":{},"labels":{"argocd.argoproj.io/instance":"str-int_int-flink-playground"},"name":"flink-vms-application-mode","namespace":"str-int"},"spec":{"flinkConfiguration":{"classloader.resolve-order":"parent-first","env.java.opts.all":"--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED","job.autoscaler.enabled":"true","job.autoscaler.memory.tuning.enabled":"false","job.autoscaler.memory.tuning.overhead":"0.5","job.autoscaler.metrics.window":"5m","job.autoscaler.restart.time":"2m","job.autoscaler.scaling.enabled":"true","job.autoscaler.stabilization.interval":"1m","job.autoscaler.target.utilization":"0.6","job.autoscaler.target.utilization.boundary":"0.2","jobmanager.memory.process.size":"1GB","jobmanager.scheduler":"adaptive","kubernetes.container.image.pull-policy":"Always","kubernetes.container.image.pull-secrets":"mega-container-registry-secret","*********************************":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","kubernetes.taskmanager.annotations":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","metrics.reporter.prom.factory.class":"org.apache.flink.metrics.prometheus.PrometheusReporterFactory","metrics.reporter.prom.port":"9249","rest.flamegraph.enabled":"true","rest.flamegraph.refresh-interval":"10m","rest.profiling.enabled":"true","taskmanager.memory.managed.fraction":"0.1","taskmanager.memory.process.size":"2GB","taskmanager.numberOfTaskSlots":"2"},"flinkVersion":"v1_20","image":"crmegahubwesteurope.azurecr.io/str-int/vms-postgres-job:1.0-SNAPSHOT","ingress":{"annotations":{"nginx.ingress.kubernetes.io/auth-realm":"Authentication Required - Flink VMS Job","nginx.ingress.kubernetes.io/auth-secret":"ude-str-flink-basic-auth","nginx.ingress.kubernetes.io/auth-type":"basic","nginx.ingress.kubernetes.io/rewrite-target":"/$2"},"className":"nginx","template":"str-int.apps.mega.cariad.cloud/{{name}}(/|$)(.*)"},"job":{"entryClass":"org.ude.VmsConnectorJob","jarURI":"local:///opt/flink/lib/flink-job-all.jar","parallelism":1,"upgradeMode":"stateless"},"jobManager":{"resource":{"cpu":1,"memory":"2Gi"}},"logConfiguration":{"logback-console.xml":"\u003cconfiguration\u003e\n  \u003cproperty name=\"pattern\" value=\"%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread) %cyan(%-12.36logger{36}) %msg%n\" /\u003e\n  \u003cappender name=\"STDOUT\" class=\"ch.qos.logback.core.ConsoleAppender\"\u003e\n      \u003ctarget\u003eSystem.out\u003c/target\u003e\n      \u003cimmediateFlush\u003etrue\u003c/immediateFlush\u003e\n      \u003cfilter class=\"ch.qos.logback.classic.filter.LevelFilter\"\u003e\n          \u003clevel\u003eERROR\u003c/level\u003e\n          \u003conMatch\u003eDENY\u003c/onMatch\u003e\n      \u003c/filter\u003e\n      \u003cfilter class=\"ch.qos.logback.classic.filter.LevelFilter\"\u003e\n          \u003clevel\u003eWARN\u003c/level\u003e\n          \u003conMatch\u003eDENY\u003c/onMatch\u003e\n      \u003c/filter\u003e\n      \u003cencoder\u003e\n          \u003cpattern\u003e${pattern}\u003c/pattern\u003e\n      \u003c/encoder\u003e\n  \u003c/appender\u003e\n\n  \u003cappender name=\"STDERR\" class=\"ch.qos.logback.core.ConsoleAppender\"\u003e\n      \u003ctarget\u003eSystem.err\u003c/target\u003e\n      \u003cimmediateFlush\u003etrue\u003c/immediateFlush\u003e\n      \u003cfilter class=\"ch.qos.logback.classic.filter.ThresholdFilter\"\u003e\n          \u003clevel\u003eWARN\u003c/level\u003e\n      \u003c/filter\u003e\n      \u003cencoder\u003e\n          \u003cpattern\u003e${pattern}\u003c/pattern\u003e\n      \u003c/encoder\u003e\n  \u003c/appender\u003e\n\n  \u003croot level=\"WARN\"\u003e\n      \u003cappender-ref ref=\"STDOUT\" /\u003e\n      \u003cappender-ref ref=\"STDERR\" /\u003e\n  \u003c/root\u003e\n\n  \u003c!-- Specific loggers --\u003e\n  \u003clogger name=\"akka\" level=\"WARN\"/\u003e\n  \u003clogger name=\"org.apache.kafka\" level=\"WARN\"/\u003e\n  \u003clogger name=\"org.apache.hadoop\" level=\"WARN\"/\u003e\n  \u003clogger name=\"org.apache.zookeeper\" level=\"WARN\"/\u003e\n  \u003clogger name=\"org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline\" level=\"OFF\"/\u003e\n\u003c/configuration\u003e\n"},"mode":"native","podTemplate":{"metadata":{"labels":{"app.kubernetes.io/name":"flink-vms-application-mode"}},"spec":{"containers":[{"env":[{"name":"env.java.opts.all","value":"--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED"},{"name":"BOOTSTRAP_SERVERS","value":"pkc-vr3yjz.westeurope.azure.confluent.cloud:9092"},{"name":"KAFKA_CLUSTER_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_secret","name":"ude-str-flink-gdc-job-kafka-api-secret"}}},{"name":"KAFKA_CLUSTER_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_key","name":"ude-str-flink-gdc-job-kafka-api-secret"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"postgresql_admin_password","name":"ude-str-flink-gdc-job-db-password"}}},{"name":"AZURE_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"ude-str-flink-store-and-explore-spn-secret"}}},{"name":"AZURE_STORAGE_KEY","valueFrom":{"secretKeyRef":{"key":"flink-storage-access-key","name":"ude-str-flink-storage-access-key-secret"}}},{"name":"KAFKA_VMS_SOURCE_TOPIC_NAME","value":"private.ude-str.vms-connector.flattened.json"},{"name":"FLINK_GROUP_ID","value":"flink-vms-job"},{"name":"DB_URL","value":"******************************************************************************************"},{"name":"DB_USER_NAME","value":"psqladmin"},{"name":"DB_VMS_DATABASE_NAME","value":"vms"},{"name":"DB_VMS_VEHICLE_DATA_TABLE_NAME","value":"vehicle_data"},{"name":"HADOOP_OPTIONAL_TOOLS","value":"hadoop-azure"},{"name":"AZURE_CREDENTIAL_TENANT_ID","value":"c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"},{"name":"AZURE_CREDENTIAL_CLIENT_ID","value":"39f7f81d-e6e9-4a20-9827-a333d98fdc66"}],"name":"flink-main-container","securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"runAsNonRoot":true,"runAsUser":9999},"volumeMounts":[{"mountPath":"/opt/flink/log","name":"flink-logs"},{"mountPath":"/opt/flink/downloads","name":"downloads"}]}],"volumes":[{"emptyDir":{},"name":"flink-logs"},{"emptyDir":{},"name":"downloads"}]}},"serviceAccount":"flink","taskManager":{"resource":{"cpu":1,"memory":"2Gi"}}}}
  creationTimestamp: "2025-05-09T08:40:32Z"
  finalizers:
  - flinkdeployments.flink.apache.org/finalizer
  generation: 2
  labels:
    argocd.argoproj.io/instance: str-int_int-flink-playground
  name: flink-vms-application-mode
  namespace: str-int
  resourceVersion: "**********"
  uid: 91d6a94c-aca5-4337-8c32-0d3b3480ad9b
spec:
  flinkConfiguration:
    classloader.resolve-order: parent-first
    env.java.opts.all: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED
      --add-opens=java.base/java.time=ALL-UNNAMED
    job.autoscaler.enabled: "true"
    job.autoscaler.memory.tuning.enabled: "false"
    job.autoscaler.memory.tuning.overhead: "0.5"
    job.autoscaler.metrics.window: 5m
    job.autoscaler.restart.time: 2m
    job.autoscaler.scaling.enabled: "true"
    job.autoscaler.stabilization.interval: 1m
    job.autoscaler.target.utilization: "0.6"
    job.autoscaler.target.utilization.boundary: "0.2"
    jobmanager.memory.process.size: 1GB
    jobmanager.scheduler: adaptive
    kubernetes.container.image.pull-policy: Always
    kubernetes.container.image.pull-secrets: mega-container-registry-secret
    *********************************: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/
    kubernetes.taskmanager.annotations: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/
    metrics.reporter.prom.factory.class: org.apache.flink.metrics.prometheus.PrometheusReporterFactory
    metrics.reporter.prom.port: "9249"
    rest.flamegraph.enabled: "true"
    rest.flamegraph.refresh-interval: 10m
    rest.profiling.enabled: "true"
    taskmanager.memory.managed.fraction: "0.1"
    taskmanager.memory.process.size: 2GB
    taskmanager.numberOfTaskSlots: "2"
  flinkVersion: v1_20
  image: crmegahubwesteurope.azurecr.io/str-int/vms-postgres-job:1.0-SNAPSHOT
  ingress:
    annotations:
      nginx.ingress.kubernetes.io/auth-realm: Authentication Required - Flink VMS
        Job
      nginx.ingress.kubernetes.io/auth-secret: ude-str-flink-basic-auth
      nginx.ingress.kubernetes.io/auth-type: basic
      nginx.ingress.kubernetes.io/rewrite-target: /$2
    className: nginx
    template: str-int.apps.mega.cariad.cloud/{{name}}(/|$)(.*)
  job:
    args: []
    entryClass: org.ude.VmsConnectorJob
    jarURI: local:///opt/flink/lib/flink-job-all.jar
    parallelism: 1
    state: running
    upgradeMode: stateless
  jobManager:
    replicas: 1
    resource:
      cpu: 1
      memory: 2Gi
  logConfiguration:
    logback-console.xml: |
      <configuration>
        <property name="pattern" value="%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread) %cyan(%-12.36logger{36}) %msg%n" />
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.out</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>ERROR</level>
                <onMatch>DENY</onMatch>
            </filter>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>WARN</level>
                <onMatch>DENY</onMatch>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <appender name="STDERR" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.err</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>WARN</level>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <root level="WARN">
            <appender-ref ref="STDOUT" />
            <appender-ref ref="STDERR" />
        </root>

        <!-- Specific loggers -->
        <logger name="akka" level="WARN"/>
        <logger name="org.apache.kafka" level="WARN"/>
        <logger name="org.apache.hadoop" level="WARN"/>
        <logger name="org.apache.zookeeper" level="WARN"/>
        <logger name="org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline" level="OFF"/>
      </configuration>
  mode: native
  podTemplate:
    metadata:
      labels:
        app.kubernetes.io/name: flink-vms-application-mode
    spec:
      containers:
      - env:
        - name: env.java.opts.all
          value: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED
            --add-opens=java.base/java.time=ALL-UNNAMED
        - name: BOOTSTRAP_SERVERS
          value: pkc-vr3yjz.westeurope.azure.confluent.cloud:9092
        - name: KAFKA_CLUSTER_API_SECRET
          valueFrom:
            secretKeyRef:
              key: confluent_kafka_api_secret
              name: ude-str-flink-gdc-job-kafka-api-secret
        - name: KAFKA_CLUSTER_API_KEY
          valueFrom:
            secretKeyRef:
              key: confluent_kafka_api_key
              name: ude-str-flink-gdc-job-kafka-api-secret
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: postgresql_admin_password
              name: ude-str-flink-gdc-job-db-password
        - name: AZURE_CREDENTIAL_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: azure_credential_client_secret
              name: ude-str-flink-store-and-explore-spn-secret
        - name: AZURE_STORAGE_KEY
          valueFrom:
            secretKeyRef:
              key: flink-storage-access-key
              name: ude-str-flink-storage-access-key-secret
        - name: KAFKA_VMS_SOURCE_TOPIC_NAME
          value: private.ude-str.vms-connector.flattened.json
        - name: FLINK_GROUP_ID
          value: flink-vms-job
        - name: DB_URL
          value: ******************************************************************************************
        - name: DB_USER_NAME
          value: psqladmin
        - name: DB_VMS_DATABASE_NAME
          value: vms
        - name: DB_VMS_VEHICLE_DATA_TABLE_NAME
          value: vehicle_data
        - name: HADOOP_OPTIONAL_TOOLS
          value: hadoop-azure
        - name: AZURE_CREDENTIAL_TENANT_ID
          value: c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c
        - name: AZURE_CREDENTIAL_CLIENT_ID
          value: 39f7f81d-e6e9-4a20-9827-a333d98fdc66
        name: flink-main-container
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          runAsUser: 9999
        volumeMounts:
        - mountPath: /opt/flink/log
          name: flink-logs
        - mountPath: /opt/flink/downloads
          name: downloads
      volumes:
      - emptyDir: {}
        name: flink-logs
      - emptyDir: {}
        name: downloads
  serviceAccount: flink
  taskManager:
    resource:
      cpu: 1
      memory: 2Gi
status:
  clusterInfo:
    flink-revision: cb1e7b5 @ 2025-01-28T23:32:02+01:00
    flink-version: 1.20.1
    total-cpu: "1.0"
    total-memory: "**********"
  error: '{"type":"org.apache.flink.util.SerializedThrowable","message":"org.apache.flink.runtime.JobException:
    Recovery is suppressed by NoRestartBackoffTimeStrategy","additionalMetadata":{},"throwableList":[{"type":"org.apache.flink.util.SerializedThrowable","message":"org.apache.flink.util.FlinkExpectedException:
    The TaskExecutor is shutting down.","additionalMetadata":{}}]}'
  jobManagerDeploymentStatus: MISSING
  jobStatus:
    checkpointInfo:
      lastPeriodicCheckpointTimestamp: 0
    jobId: d37e1080aa5ea382ceb8bb383d6e4a5e
    jobName: Kafka2postgres
    savepointInfo:
      lastPeriodicSavepointTimestamp: 0
      savepointHistory: []
    startTime: "*************"
    state: FAILED
    updateTime: "*************"
  lifecycleState: FAILED
  observedGeneration: 2
  reconciliationStatus:
    lastReconciledSpec: '{"spec":{"job":{"jarURI":"local:///opt/flink/lib/flink-job-all.jar","parallelism":1,"entryClass":"org.ude.VmsConnectorJob","args":[],"state":"running","savepointTriggerNonce":null,"initialSavepointPath":null,"checkpointTriggerNonce":null,"upgradeMode":"stateless","allowNonRestoredState":null,"savepointRedeployNonce":null,"autoscalerResetNonce":null},"restartNonce":null,"flinkConfiguration":{"classloader.resolve-order":"parent-first","env.java.opts.all":"--add-opens=java.base/java.lang=ALL-UNNAMED
      --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED","job.autoscaler.enabled":"true","job.autoscaler.memory.tuning.enabled":"false","job.autoscaler.memory.tuning.overhead":"0.5","job.autoscaler.metrics.window":"5m","job.autoscaler.restart.time":"2m","job.autoscaler.scaling.enabled":"true","job.autoscaler.stabilization.interval":"1m","job.autoscaler.target.utilization":"0.6","job.autoscaler.target.utilization.boundary":"0.2","jobmanager.memory.process.size":"1GB","jobmanager.scheduler":"adaptive","kubernetes.container.image.pull-policy":"Always","kubernetes.container.image.pull-secrets":"mega-container-registry-secret","*********************************":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","kubernetes.taskmanager.annotations":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","metrics.reporter.prom.factory.class":"org.apache.flink.metrics.prometheus.PrometheusReporterFactory","metrics.reporter.prom.port":"9249","rest.flamegraph.enabled":"true","rest.flamegraph.refresh-interval":"10m","rest.profiling.enabled":"true","taskmanager.memory.managed.fraction":"0.1","taskmanager.memory.process.size":"2GB","taskmanager.numberOfTaskSlots":"2"},"image":"crmegahubwesteurope.azurecr.io/str-int/vms-postgres-job:1.0-SNAPSHOT","imagePullPolicy":null,"serviceAccount":"flink","flinkVersion":"v1_20","ingress":{"template":"str-int.apps.mega.cariad.cloud/{{name}}(/|$)(.*)","className":"nginx","annotations":{"nginx.ingress.kubernetes.io/auth-realm":"Authentication
      Required - Flink VMS Job","nginx.ingress.kubernetes.io/auth-secret":"ude-str-flink-basic-auth","nginx.ingress.kubernetes.io/auth-type":"basic","nginx.ingress.kubernetes.io/rewrite-target":"/$2"},"labels":null,"tls":null},"podTemplate":{"metadata":{"labels":{"app.kubernetes.io/name":"flink-vms-application-mode"}},"spec":{"containers":[{"env":[{"name":"env.java.opts.all","value":"--add-opens=java.base/java.lang=ALL-UNNAMED
      --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED"},{"name":"BOOTSTRAP_SERVERS","value":"pkc-vr3yjz.westeurope.azure.confluent.cloud:9092"},{"name":"KAFKA_CLUSTER_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_secret","name":"ude-str-flink-gdc-job-kafka-api-secret"}}},{"name":"KAFKA_CLUSTER_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_key","name":"ude-str-flink-gdc-job-kafka-api-secret"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"postgresql_admin_password","name":"ude-str-flink-gdc-job-db-password"}}},{"name":"AZURE_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"ude-str-flink-store-and-explore-spn-secret"}}},{"name":"AZURE_STORAGE_KEY","valueFrom":{"secretKeyRef":{"key":"flink-storage-access-key","name":"ude-str-flink-storage-access-key-secret"}}},{"name":"KAFKA_VMS_SOURCE_TOPIC_NAME","value":"private.ude-str.vms-connector.flattened.json"},{"name":"FLINK_GROUP_ID","value":"flink-vms-job"},{"name":"DB_URL","value":"******************************************************************************************"},{"name":"DB_USER_NAME","value":"psqladmin"},{"name":"DB_VMS_DATABASE_NAME","value":"vms"},{"name":"DB_VMS_VEHICLE_DATA_TABLE_NAME","value":"vehicle_data"},{"name":"HADOOP_OPTIONAL_TOOLS","value":"hadoop-azure"},{"name":"AZURE_CREDENTIAL_TENANT_ID","value":"c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"},{"name":"AZURE_CREDENTIAL_CLIENT_ID","value":"39f7f81d-e6e9-4a20-9827-a333d98fdc66"}],"name":"flink-main-container","securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"runAsNonRoot":true,"runAsUser":9999},"volumeMounts":[{"mountPath":"/opt/flink/log","name":"flink-logs"},{"mountPath":"/opt/flink/downloads","name":"downloads"}]}],"volumes":[{"emptyDir":{},"name":"flink-logs"},{"emptyDir":{},"name":"downloads"}]}},"jobManager":{"resource":{"cpu":1.0,"memory":"2Gi","ephemeralStorage":null},"replicas":1,"podTemplate":null},"taskManager":{"resource":{"cpu":1.0,"memory":"2Gi","ephemeralStorage":null},"replicas":null,"podTemplate":null},"logConfiguration":{"logback-console.xml":"<configuration>\n  <property
      name=\"pattern\" value=\"%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread)
      %cyan(%-12.36logger{36}) %msg%n\" />\n  <appender name=\"STDOUT\" class=\"ch.qos.logback.core.ConsoleAppender\">\n      <target>System.out</target>\n      <immediateFlush>true</immediateFlush>\n      <filter
      class=\"ch.qos.logback.classic.filter.LevelFilter\">\n          <level>ERROR</level>\n          <onMatch>DENY</onMatch>\n      </filter>\n      <filter
      class=\"ch.qos.logback.classic.filter.LevelFilter\">\n          <level>WARN</level>\n          <onMatch>DENY</onMatch>\n      </filter>\n      <encoder>\n          <pattern>${pattern}</pattern>\n      </encoder>\n  </appender>\n\n  <appender
      name=\"STDERR\" class=\"ch.qos.logback.core.ConsoleAppender\">\n      <target>System.err</target>\n      <immediateFlush>true</immediateFlush>\n      <filter
      class=\"ch.qos.logback.classic.filter.ThresholdFilter\">\n          <level>WARN</level>\n      </filter>\n      <encoder>\n          <pattern>${pattern}</pattern>\n      </encoder>\n  </appender>\n\n  <root
      level=\"WARN\">\n      <appender-ref ref=\"STDOUT\" />\n      <appender-ref
      ref=\"STDERR\" />\n  </root>\n\n  <!-- Specific loggers -->\n  <logger name=\"akka\"
      level=\"WARN\"/>\n  <logger name=\"org.apache.kafka\" level=\"WARN\"/>\n  <logger
      name=\"org.apache.hadoop\" level=\"WARN\"/>\n  <logger name=\"org.apache.zookeeper\"
      level=\"WARN\"/>\n  <logger name=\"org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline\"
      level=\"OFF\"/>\n</configuration>\n"},"mode":"native"},"resource_metadata":{"apiVersion":"flink.apache.org/v1beta1","firstDeployment":true}}'
    lastStableSpec: '{"spec":{"job":{"jarURI":"local:///opt/flink/lib/flink-job-all.jar","parallelism":1,"entryClass":"org.ude.VmsConnectorJob","args":[],"state":"running","savepointTriggerNonce":null,"initialSavepointPath":null,"checkpointTriggerNonce":null,"upgradeMode":"stateless","allowNonRestoredState":null,"savepointRedeployNonce":null,"autoscalerResetNonce":null},"restartNonce":null,"flinkConfiguration":{"classloader.resolve-order":"parent-first","env.java.opts.all":"--add-opens=java.base/java.lang=ALL-UNNAMED
      --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED","job.autoscaler.enabled":"true","job.autoscaler.memory.tuning.enabled":"false","job.autoscaler.memory.tuning.overhead":"0.5","job.autoscaler.metrics.window":"5m","job.autoscaler.restart.time":"2m","job.autoscaler.scaling.enabled":"true","job.autoscaler.stabilization.interval":"1m","job.autoscaler.target.utilization":"0.6","job.autoscaler.target.utilization.boundary":"0.2","jobmanager.memory.process.size":"1GB","jobmanager.scheduler":"adaptive","kubernetes.container.image.pull-policy":"Always","kubernetes.container.image.pull-secrets":"mega-container-registry-secret","*********************************":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","kubernetes.taskmanager.annotations":"prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/","metrics.reporter.prom.factory.class":"org.apache.flink.metrics.prometheus.PrometheusReporterFactory","metrics.reporter.prom.port":"9249","rest.flamegraph.enabled":"true","rest.flamegraph.refresh-interval":"10m","rest.profiling.enabled":"true","taskmanager.memory.managed.fraction":"0.1","taskmanager.memory.process.size":"2GB","taskmanager.numberOfTaskSlots":"2"},"image":"crmegahubwesteurope.azurecr.io/str-int/vms-postgres-job:1.0-SNAPSHOT","imagePullPolicy":null,"serviceAccount":"flink","flinkVersion":"v1_20","ingress":{"template":"str-int.apps.mega.cariad.cloud/{{name}}(/|$)(.*)","className":"nginx","annotations":{"nginx.ingress.kubernetes.io/auth-realm":"Authentication
      Required - Flink VMS Job","nginx.ingress.kubernetes.io/auth-secret":"ude-str-flink-basic-auth","nginx.ingress.kubernetes.io/auth-type":"basic","nginx.ingress.kubernetes.io/rewrite-target":"/$2"},"labels":null,"tls":null},"podTemplate":{"metadata":{"labels":{"app.kubernetes.io/name":"flink-vms-application-mode"}},"spec":{"containers":[{"env":[{"name":"env.java.opts.all","value":"--add-opens=java.base/java.lang=ALL-UNNAMED
      --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED"},{"name":"BOOTSTRAP_SERVERS","value":"pkc-vr3yjz.westeurope.azure.confluent.cloud:9092"},{"name":"KAFKA_CLUSTER_API_SECRET","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_secret","name":"ude-str-flink-gdc-job-kafka-api-secret"}}},{"name":"KAFKA_CLUSTER_API_KEY","valueFrom":{"secretKeyRef":{"key":"confluent_kafka_api_key","name":"ude-str-flink-gdc-job-kafka-api-secret"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"postgresql_admin_password","name":"ude-str-flink-gdc-job-db-password"}}},{"name":"AZURE_CREDENTIAL_CLIENT_SECRET","valueFrom":{"secretKeyRef":{"key":"azure_credential_client_secret","name":"ude-str-flink-store-and-explore-spn-secret"}}},{"name":"AZURE_STORAGE_KEY","valueFrom":{"secretKeyRef":{"key":"flink-storage-access-key","name":"ude-str-flink-storage-access-key-secret"}}},{"name":"KAFKA_VMS_SOURCE_TOPIC_NAME","value":"private.ude-str.vms-connector.flattened.json"},{"name":"FLINK_GROUP_ID","value":"flink-vms-job"},{"name":"DB_URL","value":"******************************************************************************************"},{"name":"DB_USER_NAME","value":"psqladmin"},{"name":"DB_VMS_DATABASE_NAME","value":"vms"},{"name":"DB_VMS_VEHICLE_DATA_TABLE_NAME","value":"vehicle_data"},{"name":"HADOOP_OPTIONAL_TOOLS","value":"hadoop-azure"},{"name":"AZURE_CREDENTIAL_TENANT_ID","value":"c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"},{"name":"AZURE_CREDENTIAL_CLIENT_ID","value":"39f7f81d-e6e9-4a20-9827-a333d98fdc66"}],"name":"flink-main-container","securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"runAsNonRoot":true,"runAsUser":9999},"volumeMounts":[{"mountPath":"/opt/flink/log","name":"flink-logs"},{"mountPath":"/opt/flink/downloads","name":"downloads"}]}],"volumes":[{"emptyDir":{},"name":"flink-logs"},{"emptyDir":{},"name":"downloads"}]}},"jobManager":{"resource":{"cpu":1.0,"memory":"2Gi","ephemeralStorage":null},"replicas":1,"podTemplate":null},"taskManager":{"resource":{"cpu":1.0,"memory":"2Gi","ephemeralStorage":null},"replicas":null,"podTemplate":null},"logConfiguration":{"logback-console.xml":"<configuration>\n  <property
      name=\"pattern\" value=\"%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread)
      %cyan(%-12.36logger{36}) %msg%n\" />\n  <appender name=\"STDOUT\" class=\"ch.qos.logback.core.ConsoleAppender\">\n      <target>System.out</target>\n      <immediateFlush>true</immediateFlush>\n      <filter
      class=\"ch.qos.logback.classic.filter.LevelFilter\">\n          <level>ERROR</level>\n          <onMatch>DENY</onMatch>\n      </filter>\n      <filter
      class=\"ch.qos.logback.classic.filter.LevelFilter\">\n          <level>WARN</level>\n          <onMatch>DENY</onMatch>\n      </filter>\n      <encoder>\n          <pattern>${pattern}</pattern>\n      </encoder>\n  </appender>\n\n  <appender
      name=\"STDERR\" class=\"ch.qos.logback.core.ConsoleAppender\">\n      <target>System.err</target>\n      <immediateFlush>true</immediateFlush>\n      <filter
      class=\"ch.qos.logback.classic.filter.ThresholdFilter\">\n          <level>WARN</level>\n      </filter>\n      <encoder>\n          <pattern>${pattern}</pattern>\n      </encoder>\n  </appender>\n\n  <root
      level=\"WARN\">\n      <appender-ref ref=\"STDOUT\" />\n      <appender-ref
      ref=\"STDERR\" />\n  </root>\n\n  <!-- Specific loggers -->\n  <logger name=\"akka\"
      level=\"WARN\"/>\n  <logger name=\"org.apache.kafka\" level=\"WARN\"/>\n  <logger
      name=\"org.apache.hadoop\" level=\"WARN\"/>\n  <logger name=\"org.apache.zookeeper\"
      level=\"WARN\"/>\n  <logger name=\"org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline\"
      level=\"OFF\"/>\n</configuration>\n"},"mode":"native"},"resource_metadata":{"apiVersion":"flink.apache.org/v1beta1","firstDeployment":true}}'
    reconciliationTimestamp: 1746780038455
    state: DEPLOYED
  taskManager:
    labelSelector: component=taskmanager,app=flink-vms-application-mode
    replicas: 1
