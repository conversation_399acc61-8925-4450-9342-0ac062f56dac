on:
  workflow_dispatch:

    inputs:
      environment:
        description: 'The environment to apply the Terraform configuration to'
        required: true
        type: environment
      application:
        type: choice
        description: ArgoCD application.yaml to apply
        options: 
        - akhq
        - confluent-proxy
        - deployment-service
        - data-ingestion-api
        - data-protection-service
        - management-api
        - flink-playground
        - otel-azure-monitor
        - postgresql-proxy
        - signal-definition-retrieval-service
permissions:
  id-token: write # This is required for requesting the JWT
  contents: read  # This is required for actions/checkout
  pull-requests: write
run-name: kubectl Apply for ${{ inputs.environment }}/${{ inputs.application }}


jobs:
  kubectl-apply:
    strategy:
      matrix:
        include:
          - environment: dev
            runner: ['self-hosted', 'MegatronProducts', 'westeurope']
          - environment: int
            runner: ['self-hosted', 'MegatronProducts', 'westeurope']
          - environment: pre
            runner: ['self-hosted', 'MegatronProducts', 'westeurope']
          - environment: pre-us
            runner: ['self-hosted', 'MegatronProducts', 'eastus2']
          - environment: prd-eu
            runner: ['self-hosted', 'MegatronProducts', 'westeurope']
          - environment: prd-us
            runner: ['self-hosted', 'MegatronProducts', 'eastus2']
    if: contains(fromJSON('["dev", "int", "pre", "pre-us", "prd-eu", "prd-us"]'), github.event.inputs.environment)

    environment: ${{ inputs.environment }}
    runs-on: ${{ matrix.runner }}

    steps:
    - name: Checkout Code
      if: ${{ matrix.environment == github.event.inputs.environment }}
      uses: actions/checkout@v2

    - name: 'Az CLI login'
      if: ${{ matrix.environment == github.event.inputs.environment }}
      uses: azure/login@v1
      with:
        client-id: ${{ secrets.AZURE_CLIENT_ID }}
        tenant-id: ${{ secrets.AZURE_TENANT_ID }}
        subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}

    - name: Set kubernetes context
      if: ${{ matrix.environment == github.event.inputs.environment }}
      run: |
        az aks get-credentials -g ${{ vars.MEGA_RESOURCE_GROUP }} -n ${{ vars.MEGA_RESOURCE_GROUP }}-kubernetes
        kubelogin convert-kubeconfig -l azurecli

    - name: Kubectl Apply
      if: ${{ matrix.environment == github.event.inputs.environment }}
      run: kubectl apply -f ./argocd/applications/${{ inputs.environment }}/${{ inputs.application }}.yaml
