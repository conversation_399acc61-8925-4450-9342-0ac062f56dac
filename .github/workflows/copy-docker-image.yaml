name: Copy Docker Image
on:
  workflow_dispatch:
    inputs:
      source-image:
        description: 'Source Image (e.g. alpine/socat:*******)'
        required: true
      dest-image-name:
        description: 'Destination Image Name (e.g. socat-*******)'
      environment:
        type: environment
        description: "Environment to copy to"

jobs:
  copy-image-to-acr:
    if: contains(fromJSON('["dev", "int", "pre", "pre-us"]'), github.event.inputs.environment)
    name: Copy Image
    uses: cariad-ude/ude-str-github-actions/.github/workflows/copy-image-to-acr.yaml@main
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      source-image: ${{ inputs.source-image }}
      dest-image-name: ${{ inputs.dest-image-name }}
