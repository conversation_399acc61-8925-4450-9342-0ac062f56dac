[2025-06-10 18:01:59] Starting script to update FlinkDeployment 'gdc-1b7a3957-3729-4383-8d5c-6cf16dcd647e' in namespace 'str-int'
[2025-06-10 18:01:59] Running in DRY-RUN mode - no changes will be applied
[2025-06-10 18:01:59] Savepoint functionality enabled - job will be gracefully stopped with savepoint
[2025-06-10 18:01:59] Savepoint base path: abfss://<EMAIL>/gdc-eb6c67ac-a133-43b0-a4dc-a0de473fef18/savepoints
[2025-06-10 18:02:00] No REST API URL provided - will use port-forwarding
[2025-06-10 18:02:00] Checking if gdc-1b7a3957-3729-4383-8d5c-6cf16dcd647e needs STORAGE_SCHEMA_ID...
[2025-06-10 18:02:01] STORAGE_SCHEMA_ID is missing in gdc-1b7a3957-3729-4383-8d5c-6cf16dcd647e, extracting from ENRICHMENT_CONFIG...
[2025-06-10 18:02:02] Found schemaId: 7b3ad26d-f90a-4370-acc6-bc8bb1b1dfa7
[2025-06-10 18:02:02] [DRY-RUN] Would create savepoint at: abfss://<EMAIL>/gdc-eb6c67ac-a133-43b0-a4dc-a0de473fef18/savepoints/gdc-1b7a3957-3729-4383-8d5c-6cf16dcd647e-savepoint-1749571322
[2025-06-10 18:02:02] [DRY-RUN] Would add STORAGE_SCHEMA_ID=7b3ad26d-f90a-4370-acc6-bc8bb1b1dfa7, update restartNonce to 1749571322, and use savepoint
[2025-06-10 18:02:02] Operation completed
[2025-06-10 18:02:02] Log file: flinkdeployment-update-20250610-180159.log
[2025-06-10 18:02:02] This was a dry run. No changes were applied.
