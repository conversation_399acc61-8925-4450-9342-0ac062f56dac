#!/bin/bash
# Script to add STORAGE_SCHEMA_ID to a single FlinkDeployment and restart it
# This script supports atomic patching and optional savepoint functionality

# Default values
NAMESPACE=""
DEPLOYMENT_NAME=""
DRY_RUN=false
LOG_FILE="flinkdeployment-update-$(date +%Y%m%d-%H%M%S).log"
USE_SAVEPOINT=false
SAVEPOINT_BASE_PATH=""
FLINK_REST_URL=""
PORT_FORWARD_PID=""

# Function to display usage information
usage() {
  echo "Usage: $0 [OPTIONS]"
  echo "Options:"
  echo "  -n, --namespace NAMESPACE    Specify the Kubernetes namespace (required)"
  echo "  -d, --deployment NAME        Specify the FlinkDeployment name (required)"
  echo "  -r, --dry-run                Show what would be changed without applying"
  echo "  -s, --savepoint              Enable savepoint functionality for graceful restart"
  echo "  -p, --savepoint-path PATH    Base path for savepoint storage (required with -s)"
  echo "  -u, --rest-url URL           Public URL for Flink JobManager REST API"
  echo "                               (if not provided, port-forwarding will be used)"
  echo "  -h, --help                   Display this help message"
  exit 1
}

# Function for logging
log() {
  local message="$1"
  local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] $message"
  echo "[$timestamp] $message" >> "$LOG_FILE"
}

# Function to clean up port-forwarding
cleanup() {
  if [ -n "$PORT_FORWARD_PID" ]; then
    log "Cleaning up port-forward (PID: $PORT_FORWARD_PID)"
    kill "$PORT_FORWARD_PID" >/dev/null 2>&1 || true
  fi
}

# Set up trap for cleanup
trap cleanup EXIT

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    -n|--namespace)
      NAMESPACE="$2"
      shift 2
      ;;
    -d|--deployment)
      DEPLOYMENT_NAME="$2"
      shift 2
      ;;
    -r|--dry-run)
      DRY_RUN=true
      shift
      ;;
    -s|--savepoint)
      USE_SAVEPOINT=true
      shift
      ;;
    -p|--savepoint-path)
      SAVEPOINT_BASE_PATH="$2"
      shift 2
      ;;
    -u|--rest-url)
      FLINK_REST_URL="$2"
      shift 2
      ;;
    -h|--help)
      usage
      ;;
    *)
      echo "Unknown option: $1"
      usage
      ;;
  esac
done

# Validate required parameters
if [ -z "$NAMESPACE" ]; then
  echo "Error: Namespace is required"
  usage
fi

if [ -z "$DEPLOYMENT_NAME" ]; then
  echo "Error: FlinkDeployment name is required"
  usage
fi

if [ "$USE_SAVEPOINT" = true ] && [ -z "$SAVEPOINT_BASE_PATH" ]; then
  echo "Error: Savepoint path is required when using savepoint functionality"
  usage
fi

# Check if required tools are available
if ! command -v kubectl &> /dev/null; then
  echo "Error: kubectl is not installed or not in PATH"
  exit 1
fi

if ! command -v jq &> /dev/null; then
  echo "Error: jq is not installed or not in PATH"
  exit 1
fi

if [ "$USE_SAVEPOINT" = true ] && ! command -v curl &> /dev/null; then
  echo "Error: curl is not installed or not in PATH (required for savepoint functionality)"
  exit 1
fi

log "Starting script to update FlinkDeployment '$DEPLOYMENT_NAME' in namespace '$NAMESPACE'"
if [ "$DRY_RUN" = true ]; then
  log "Running in DRY-RUN mode - no changes will be applied"
fi

if [ "$USE_SAVEPOINT" = true ]; then
  log "Savepoint functionality enabled - job will be gracefully stopped with savepoint"
  log "Savepoint base path: $SAVEPOINT_BASE_PATH"
  if [ -n "$FLINK_REST_URL" ]; then
    log "Using provided Flink REST API URL: $FLINK_REST_URL"
  else
    log "No REST API URL provided - will use port-forwarding"
  fi
fi

# Check if the specified FlinkDeployment exists
if ! kubectl get flinkdeployment "$DEPLOYMENT_NAME" -n "$NAMESPACE" &>/dev/null; then
  log "Error: FlinkDeployment '$DEPLOYMENT_NAME' not found in namespace '$NAMESPACE'"
  exit 1
fi

log "Checking if $DEPLOYMENT_NAME needs STORAGE_SCHEMA_ID..."

# Check if STORAGE_SCHEMA_ID is missing
HAS_STORAGE_SCHEMA_ID=$(kubectl get flinkdeployment "$DEPLOYMENT_NAME" -n "$NAMESPACE" -o json | 
  jq '.spec.podTemplate.spec.containers[].env | map(select(.name == "STORAGE_SCHEMA_ID")) | length')

CURRENT_SCHEMA_ID=$(kubectl get flinkdeployment "$DEPLOYMENT_NAME" -n "$NAMESPACE" -o json | 
  jq -r '.spec.podTemplate.spec.containers[].env[] | select(.name == "STORAGE_SCHEMA_ID") | .value')

if [ "$HAS_STORAGE_SCHEMA_ID" -eq "0" ]; then
  log "STORAGE_SCHEMA_ID is missing in $DEPLOYMENT_NAME. This script is for updating existing values only."
  exit 1
elif [ "$CURRENT_SCHEMA_ID" != "$SCHEMA_ID" ]; then
  log "Updating STORAGE_SCHEMA_ID from '$CURRENT_SCHEMA_ID' to '$SCHEMA_ID'..."
  
  # Find the index of the existing STORAGE_SCHEMA_ID environment variable
  ENV_VAR_INDEX=$(kubectl get flinkdeployment "$DEPLOYMENT_NAME" -n "$NAMESPACE" -o json | jq ".spec.podTemplate.spec.containers[$CONTAINER_INDEX].env | map(.name == \"STORAGE_SCHEMA_ID\") | index(true)")
  
  if [ -z "$ENV_VAR_INDEX" ] || [ "$ENV_VAR_INDEX" == "null" ]; then
    log "Error: STORAGE_SCHEMA_ID environment variable not found in $DEPLOYMENT_NAME"
    exit 1
  fi
  
  log "Found STORAGE_SCHEMA_ID at index: $ENV_VAR_INDEX"
  
  # Continue with the rest of the script...
else
  log "STORAGE_SCHEMA_ID already exists in $DEPLOYMENT_NAME with the correct value, no changes needed"
  exit 0
fi

# Extract schemaId from ENRICHMENT_CONFIG
ENRICHMENT_CONFIG=$(kubectl get flinkdeployment "$DEPLOYMENT_NAME" -n "$NAMESPACE" -o json | 
  jq -r '.spec.podTemplate.spec.containers[].env[] | select(.name == "ENRICHMENT_CONFIG") | .value')
  
if [ -z "$ENRICHMENT_CONFIG" ]; then
  log "Error: ENRICHMENT_CONFIG not found in $DEPLOYMENT_NAME"
  exit 1
fi
  
# Extract the schemaId from the ENRICHMENT_CONFIG JSON
SCHEMA_ID=$(echo "$ENRICHMENT_CONFIG" | jq -r '.[0].schemaId // empty')
  
if [ -z "$SCHEMA_ID" ]; then
  log "Error: Could not extract schemaId from ENRICHMENT_CONFIG in $DEPLOYMENT_NAME"
  exit 1
fi
  
log "Found schemaId: $SCHEMA_ID"
  
# Get the container index that has the env vars
CONTAINER_INDEX=$(kubectl get flinkdeployment "$DEPLOYMENT_NAME" -n "$NAMESPACE" -o json |  
  jq '.spec.podTemplate.spec.containers | map(.name == "flink-main-container") | index(true) // 0')
  
# Generate a restartNonce value (timestamp-based)
RESTART_NONCE=$(date +%s)
  
# Ask for confirmation if not in dry-run mode
if [ "$DRY_RUN" = false ]; then
  if [ "$USE_SAVEPOINT" = true ]; then
    read -p "Create savepoint and patch $DEPLOYMENT_NAME with STORAGE_SCHEMA_ID? (y/n): " CONFIRM_PATCH
  else
    read -p "Patch $DEPLOYMENT_NAME with STORAGE_SCHEMA_ID and restart? (y/n): " CONFIRM_PATCH
  fi
  
  if [[ ! "$CONFIRM_PATCH" =~ ^[Yy]$ ]]; then
    log "Operation cancelled by user"
    exit 0
  fi
  
  SAVEPOINT_PATH=""
  
  # Handle savepoint creation if enabled
  if [ "$USE_SAVEPOINT" = true ]; then
    log "Starting savepoint creation for $DEPLOYMENT_NAME..."
    TIMESTAMP=$(date +%s)
    SAVEPOINT_DIR="${SAVEPOINT_BASE_PATH}/${DEPLOYMENT_NAME}-savepoint-${TIMESTAMP}"
    log "Savepoint directory: $SAVEPOINT_DIR"
    
    # Set up REST API access
    REST_ENDPOINT=""
    if [ -n "$FLINK_REST_URL" ]; then
      REST_ENDPOINT="$FLINK_REST_URL"
    else
      log "Setting up port-forwarding for Flink JobManager REST API..."
      kubectl port-forward svc/${DEPLOYMENT_NAME}-rest 8081:8081 -n "$NAMESPACE" >/dev/null 2>&1 &
      PORT_FORWARD_PID=$!
      log "Port-forwarding started with PID: $PORT_FORWARD_PID"
      sleep 3
      REST_ENDPOINT="http://localhost:8081"
    fi
    
    if [ "$DRY_RUN" = false ]; then
      # Get Job ID
      log "Fetching running job ID..."
      JOB_ID=$(curl -s ${REST_ENDPOINT}/jobs/overview | jq -r '.jobs[] | select(.state=="RUNNING") | .jid')
      
      if [[ -z "$JOB_ID" ]]; then
        log "No RUNNING job found for deployment: $DEPLOYMENT_NAME"
        log "Will proceed without savepoint"
      else
        log "Found running job: $JOB_ID"
        
        # Trigger graceful stop with savepoint
        log "Stopping job with drain=true and savepoint path: $SAVEPOINT_DIR"
        STOP_RESPONSE=$(curl -s -X POST ${REST_ENDPOINT}/jobs/${JOB_ID}/stop \
          -H "Content-Type: application/json" \
          -d "{\"drain\": true, \"target-directory\": \"${SAVEPOINT_DIR}\"}")
        
        TRIGGER_ID=$(echo "$STOP_RESPONSE" | jq -r '.request-id')
        
        if [[ -z "$TRIGGER_ID" || "$TRIGGER_ID" == "null" ]]; then
          log "Failed to trigger stop with savepoint. Will proceed without savepoint."
        else
          log "Stop triggered, trigger ID: $TRIGGER_ID"
          
          # Poll job status
          log "Waiting for job to stop..."
          JOB_STOPPED=false
          for i in {1..30}; do
            JOB_STATE=$(curl -s ${REST_ENDPOINT}/jobs/${JOB_ID} | jq -r '.state')
            
            if [[ "$JOB_STATE" == "FINISHED" || "$JOB_STATE" == "CANCELED" ]]; then
              log "Job stopped with state: $JOB_STATE"
              JOB_STOPPED=true
              SAVEPOINT_PATH="$SAVEPOINT_DIR"
              break
            fi
            
            if [[ "$JOB_STATE" == "FAILED" ]]; then
              log "Job failed during stop. Will proceed without savepoint."
              break
            fi
            
            log "Job state: $JOB_STATE, waiting..."
            sleep 5
          done
          
          if [ "$JOB_STOPPED" = false ]; then
            log "Timed out waiting for job to stop. Will proceed without savepoint."
          fi
        fi
      fi
      
      # Clean up port-forwarding if we set it up
      if [ -z "$FLINK_REST_URL" ] && [ -n "$PORT_FORWARD_PID" ]; then
        kill "$PORT_FORWARD_PID" >/dev/null 2>&1 || true
        PORT_FORWARD_PID=""
        log "Port-forwarding stopped"
      fi
    else
      log "[DRY-RUN] Would create savepoint at: $SAVEPOINT_DIR"
      SAVEPOINT_PATH="$SAVEPOINT_DIR"
    fi
  fi
  
  # Create the appropriate patch based on whether we have a savepoint
  if [ -n "$SAVEPOINT_PATH" ]; then
    # Create a combined JSON patch that replaces the env var, updates restartNonce, and sets savepoint path
    PATCH=$(cat <<EOF
[
  {
    "op": "replace",
    "path": "/spec/podTemplate/spec/containers/$CONTAINER_INDEX/env/$ENV_VAR_INDEX",
    "value": {
      "name": "STORAGE_SCHEMA_ID",
      "value": "$SCHEMA_ID"
    }
  },
  {
    "op": "replace",
    "path": "/spec/restartNonce",
    "value": $RESTART_NONCE
  },
  {
    "op": "replace",
    "path": "/spec/job/initialSavepointPath",
    "value": "$SAVEPOINT_PATH"
  }
]
EOF
)
  else
    # Create a combined JSON patch that just replaces the env var and updates restartNonce
    PATCH=$(cat <<EOF
[
  {
    "op": "replace",
    "path": "/spec/podTemplate/spec/containers/$CONTAINER_INDEX/env/$ENV_VAR_INDEX",
    "value": {
      "name": "STORAGE_SCHEMA_ID",
      "value": "$SCHEMA_ID"
    }
  },
  {
    "op": "replace",
    "path": "/spec/restartNonce",
    "value": $RESTART_NONCE
  }
]
EOF
)
  fi
  
  # Display the patch
  log "Patch to be applied:"
  echo "$PATCH"
  
  if [ "$DRY_RUN" = false ]; then
    # Apply the JSON patch
    if [ -n "$SAVEPOINT_PATH" ]; then
      log "Applying patch to add STORAGE_SCHEMA_ID=$SCHEMA_ID, restart with nonce=$RESTART_NONCE, and use savepoint=$SAVEPOINT_PATH"
    else
      log "Applying patch to add STORAGE_SCHEMA_ID=$SCHEMA_ID and restart with nonce=$RESTART_NONCE"
    fi
    
    echo "$PATCH" | kubectl patch flinkdeployment "$DEPLOYMENT_NAME" -n "$NAMESPACE" --type=json --patch-file=/dev/stdin
    
    if [ $? -eq 0 ]; then
      if [ -n "$SAVEPOINT_PATH" ]; then
        log "Successfully patched $DEPLOYMENT_NAME with STORAGE_SCHEMA_ID and triggered restart from savepoint"
      else
        log "Successfully patched $DEPLOYMENT_NAME with STORAGE_SCHEMA_ID and triggered restart"
      fi
    else
      log "Failed to patch $DEPLOYMENT_NAME"
      exit 1
    fi
  else
    if [ -n "$SAVEPOINT_PATH" ]; then
      log "[DRY-RUN] Would patch $DEPLOYMENT_NAME with STORAGE_SCHEMA_ID and restart from savepoint"
    else
      log "[DRY-RUN] Would patch $DEPLOYMENT_NAME with STORAGE_SCHEMA_ID and restart"
    fi
  fi
else
  # Dry-run mode
  if [ "$USE_SAVEPOINT" = true ]; then
    TIMESTAMP=$(date +%s)
    SAVEPOINT_DIR="${SAVEPOINT_BASE_PATH}/${DEPLOYMENT_NAME}-savepoint-${TIMESTAMP}"
    log "[DRY-RUN] Would create savepoint at: $SAVEPOINT_DIR"
    log "[DRY-RUN] Would add STORAGE_SCHEMA_ID=$SCHEMA_ID, update restartNonce to $RESTART_NONCE, and use savepoint"
  else
    log "[DRY-RUN] Would add STORAGE_SCHEMA_ID=$SCHEMA_ID and update restartNonce to $RESTART_NONCE"
  fi
fi

log "Operation completed"
log "Log file: $LOG_FILE"

if [ "$DRY_RUN" = true ]; then
  log "This was a dry run. No changes were applied."
fi

echo "Done!"
