[2025-06-20 20:27:18] Starting patch and restart with savepoint for FlinkDeployment 'gdc-02716c8f-f3d0-452f-9401-2ff86858968a' in namespace 'str-int'
[2025-06-20 20:27:18] Running in DRY-RUN mode - no changes will be applied
[2025-06-20 20:27:18] Database URL will be updated to: ***************************************************************************************************
[2025-06-20 20:27:18] TaskManager CPU will be set to: 4
[2025-06-20 20:27:18] [DRY-RUN] Would port-forward Flink JobManager REST API
[2025-06-20 20:27:18] [DRY-RUN] Would fetch running job ID
[2025-06-20 20:27:18] [DRY-RUN] Using placeholder job ID: 00000000000000000000000000000000
[2025-06-20 20:27:18] [DRY-RUN] Would stop job with savepoint path: abfss://<EMAIL>/gdc-02716c8f-f3d0-452f-9401-2ff86858968a/savepoints/savepoint-1750444038
[2025-06-20 20:27:18] [DRY-RUN] Using placeholder trigger ID: mock-trigger-id
[2025-06-20 20:27:18] [DRY-RUN] Would wait for job to stop and savepoint to be created
[2025-06-20 20:27:18] [DRY-RUN] Using placeholder savepoint path: abfss://<EMAIL>/gdc-02716c8f-f3d0-452f-9401-2ff86858968a/savepoints/savepoint-1750444038/mock-savepoint-path
[2025-06-20 20:27:18] 📦 Preparing patch for FlinkDeployment...
[2025-06-20 20:27:19] Found DB_URL at index: 16, will update existing variable
[2025-06-20 20:27:19] Patch to be applied:
[2025-06-20 20:28:43] [DRY-RUN] Would patch FlinkDeployment with the above changes
[2025-06-20 20:28:43] [DRY-RUN] Would verify FlinkDeployment restart
[2025-06-20 20:28:43] [DRY-RUN] Operation completed successfully (dry run mode)
