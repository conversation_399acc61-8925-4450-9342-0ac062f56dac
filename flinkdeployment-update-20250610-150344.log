[2025-06-10 15:03:44] Starting script to update FlinkDeployment 'gdc-1b7a3957-3729-4383-8d5c-6cf16dcd647e' in namespace 'str-int'
[2025-06-10 15:03:44] Running in DRY-RUN mode - no changes will be applied
[2025-06-10 15:03:44] Savepoint functionality enabled - job will be gracefully stopped with savepoint
[2025-06-10 15:03:44] Savepoint base path: abfss://<EMAIL>/gdc-3019027a-bf6c-4403-90fd-235aa11475e4/savepoints
[2025-06-10 15:03:44] Using provided Flink REST API URL: https://str-int.apps.mega.cariad.cloud/
[2025-06-10 15:03:45] Checking if gdc-1b7a3957-3729-4383-8d5c-6cf16dcd647e needs STORAGE_SCHEMA_ID...
[2025-06-10 15:03:46] STORAGE_SCHEMA_ID is missing in gdc-1b7a3957-3729-4383-8d5c-6cf16dcd647e, extracting from ENRICHMENT_CONFIG...
[2025-06-10 15:03:46] Found schemaId: 7b3ad26d-f90a-4370-acc6-bc8bb1b1dfa7
[2025-06-10 15:03:47] [DRY-RUN] Would create savepoint at: abfss://<EMAIL>/gdc-3019027a-bf6c-4403-90fd-235aa11475e4/savepoints/gdc-1b7a3957-3729-4383-8d5c-6cf16dcd647e-savepoint-1749560627
[2025-06-10 15:03:47] [DRY-RUN] Would add STORAGE_SCHEMA_ID=7b3ad26d-f90a-4370-acc6-bc8bb1b1dfa7, update restartNonce to 1749560627, and use savepoint
[2025-06-10 15:03:47] Operation completed
[2025-06-10 15:03:47] Log file: flinkdeployment-update-20250610-150344.log
[2025-06-10 15:03:47] This was a dry run. No changes were applied.
