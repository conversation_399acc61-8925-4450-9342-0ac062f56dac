#!/bin/bash

set -euo pipefail

# === Prompt for inputs ===
read -rp "Enter the FlinkDeployment name: " FLINK_DEPLOYMENT_NAME
read -rp "Enter base savepoint path (e.g. abfss://<EMAIL>/$FLINK_DEPLOYMENT_NAME/savepoints/): " BASE_SAVEPOINT_PATH

# === Config ===
NAMESPACE="str-int"
TIMESTAMP=$(date +%s)
SAVEPOINT_DIR="${BASE_SAVEPOINT_PATH}/savepoint-${TIMESTAMP}"
LOG_FILE="restart-with-savepoint-${FLINK_DEPLOYMENT_NAME}-$(date +%Y%m%d-%H%M%S).log"

# Function for logging
log() {
  local message="$1"
  local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] $message"
  echo "[$timestamp] $message" >> "$LOG_FILE"
}

# === Step 1: Port-forward JobManager REST endpoint ===
log "🌐 Port-forwarding Flink JobManager REST API..."
PORT_FORWARD_PID=""
kubectl port-forward svc/${FLINK_DEPLOYMENT_NAME}-rest 8081:8081 -n "$NAMESPACE" >/dev/null 2>&1 &
PORT_FORWARD_PID=$!
sleep 3

cleanup() {
  log "🧹 Cleaning up port-forward"
  kill "$PORT_FORWARD_PID" >/dev/null 2>&1 || true
}
trap cleanup EXIT

# === Step 2: Get Job ID ===
log "🔍 Fetching running job ID..."
JOB_ID=$(curl -s http://localhost:8081/jobs/overview | jq -r '.jobs[] | select(.state=="RUNNING") | .jid')

if [[ -z "$JOB_ID" ]]; then
  log "❌ No RUNNING job found for deployment: $FLINK_DEPLOYMENT_NAME"
  exit 1
fi
log "✅ Found running job: $JOB_ID"

# === Step 3: Trigger graceful stop with savepoint ===
log "🛑 Stopping job with drain=true and savepoint path: $SAVEPOINT_DIR"
STOP_RESPONSE=$(curl -s -X POST http://localhost:8081/jobs/${JOB_ID}/stop \
  -H "Content-Type: application/json" \
  -d "{\"drain\": true, \"target-directory\": \"${SAVEPOINT_DIR}\"}")

TRIGGER_ID=$(echo "$STOP_RESPONSE" | jq -r '.["request-id"]')

if [[ -z "$TRIGGER_ID" || "$TRIGGER_ID" == "null" ]]; then
  log "❌ Failed to trigger stop with savepoint."
  exit 1
fi
log "🚦 Stop triggered, trigger ID: $TRIGGER_ID."


# === Step 4: Poll job status ===
log "⏳ Waiting for job to stop..."
for i in {1..30}; do
  JOB_STATE=$(curl -s http://localhost:8081/jobs/${JOB_ID} | jq -r '.state')

  if [[ "$JOB_STATE" == "FINISHED" || "$JOB_STATE" == "CANCELED" ]]; then
    log "✅ Job stopped with state: $JOB_STATE"
    break
  fi

  if [[ "$JOB_STATE" == "FAILED" ]]; then
    log "❌ Job failed during stop."
    exit 1
  fi

  sleep 5
done

NEW_SAVEPOINT_DIR=$(curl -s   http://localhost:8081/jobs/${JOB_ID}/savepoints/${TRIGGER_ID} | jq -r '.operation.location')
log "✅ Savepoint created at: $NEW_SAVEPOINT_DIR"

# === Step 5: Patch FlinkDeployment to restart from savepoint with savepointRedeployNonce ===
NONCE=$(date +%s)
log "📦 Patching FlinkDeployment with savepoint, savepointRedeployNonce: $NONCE and initialSavepointPath: $NEW_SAVEPOINT_DIR" 

kubectl patch flinkdeployment $FLINK_DEPLOYMENT_NAME -n $NAMESPACE --type merge -p \
  "{
    \"spec\": {
      \"job\": {
        \"savepointRedeployNonce\": $NONCE,
        \"initialSavepointPath\": \"$NEW_SAVEPOINT_DIR\",
        \"upgradeMode\": \"last-state\"
      }
    }
  }"

log "🚀 Restart requested from savepoint: $SAVEPOINT_DIR (savepointRedeployNonce: $NONCE) and initialSavepointPath: $NEW_SAVEPOINT_DIR"

# === Step 6: Verify FlinkDeployment restart success ===
log "⏳ Verifying FlinkDeployment restart..."

# Set timeout parameters
TIMEOUT_SECONDS=300  # 5 minutes
POLL_INTERVAL=10     # Check every 10 seconds
START_TIME=$(date +%s)
END_TIME=$((START_TIME + TIMEOUT_SECONDS))

# Poll until job is running or timeout is reached
RESTART_SUCCESS=false
while [ $(date +%s) -lt $END_TIME ]; do
  # Get current deployment status
  DEPLOYMENT_STATUS=$(kubectl get flinkdeployment $FLINK_DEPLOYMENT_NAME -n $NAMESPACE -o jsonpath='{.status.jobStatus.state}' 2>/dev/null)
  
  if [[ "$DEPLOYMENT_STATUS" == "RUNNING" ]]; then
    RESTART_SUCCESS=true
    log "✅ FlinkDeployment successfully restarted and is now RUNNING"
    break
  elif [[ "$DEPLOYMENT_STATUS" == "FAILED" ]]; then
    log "❌ FlinkDeployment restart failed - job is in FAILED state"
    
    # Get failure details if available
    FAILURE_MESSAGE=$(kubectl get flinkdeployment $FLINK_DEPLOYMENT_NAME -n $NAMESPACE -o jsonpath='{.status.jobStatus.error}' 2>/dev/null)
    if [[ -n "$FAILURE_MESSAGE" ]]; then
      log "📋 Failure details: $FAILURE_MESSAGE"
    fi
    
    break
  fi
  
  # Check for other potential error conditions in the deployment
  DEPLOYMENT_ERROR=$(kubectl get flinkdeployment $FLINK_DEPLOYMENT_NAME -n $NAMESPACE -o jsonpath='{.status.error}' 2>/dev/null)
  if [[ -n "$DEPLOYMENT_ERROR" && "$DEPLOYMENT_ERROR" != "null" ]]; then
    log "❌ Error detected in FlinkDeployment: $DEPLOYMENT_ERROR"
    break
  fi
  
  # Calculate elapsed time and remaining time
  CURRENT_TIME=$(date +%s)
  ELAPSED=$((CURRENT_TIME - START_TIME))
  REMAINING=$((TIMEOUT_SECONDS - ELAPSED))
  
  log "⏳ Current job state: $DEPLOYMENT_STATUS (Timeout in ${REMAINING}s)"
  sleep $POLL_INTERVAL
done

# Check if we timed out
if [ $(date +%s) -ge $END_TIME ] && [ "$RESTART_SUCCESS" = false ]; then
  log "⏰ Timeout reached while waiting for FlinkDeployment to restart"
  
  # Get current pods status for debugging
  log "📋 Current pods status:"
  kubectl get pods -n $NAMESPACE | grep $FLINK_DEPLOYMENT_NAME | tee -a "$LOG_FILE"
  
  # Check JobManager logs for potential issues
  log "📋 Recent JobManager logs (if available):"
  JOBMANAGER_POD=$(kubectl get pods -n $NAMESPACE | grep $FLINK_DEPLOYMENT_NAME | grep jobmanager | awk '{print $1}' | head -1)
  if [[ -n "$JOBMANAGER_POD" ]]; then
    kubectl logs $JOBMANAGER_POD -n $NAMESPACE --tail=50 | tee -a "$LOG_FILE"
  else
    log "No JobManager pod found"
  fi
fi

# Final status summary
log "=== Restart Operation Summary ==="
log "FlinkDeployment: $FLINK_DEPLOYMENT_NAME"
log "Namespace: $NAMESPACE"
log "Savepoint path: $NEW_SAVEPOINT_DIR"
log "Final state: $DEPLOYMENT_STATUS"

if [ "$RESTART_SUCCESS" = true ]; then
  log "✅ Restart operation completed successfully"
  exit 0
else
  log "❌ Restart operation did not complete successfully within the timeout period"
  exit 1
fi
