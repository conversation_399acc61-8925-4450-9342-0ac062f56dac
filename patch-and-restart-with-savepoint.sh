#!/bin/bash

set -euo pipefail

# === Default values ===
NAMESPACE=""
FLINK_DEPLOYMENT_NAME=""
BASE_SAVEPOINT_PATH=""
DB_URL=""
CPU_VALUE=""
DRY_RUN=false
LOG_FILE="patch-and-restart-with-savepoint-$(date +%Y%m%d-%H%M%S).log"

# === Function to display usage information ===
usage() {
  echo "Usage: $0 [OPTIONS]"
  echo "Options:"
  echo "  -n, --namespace NAMESPACE    Specify the Kubernetes namespace (default: str-int)"
  echo "  -d, --deployment NAME        Specify the FlinkDeployment name (required)"
  echo "  -p, --savepoint-path PATH    Base path for savepoint storage (required)"
  echo "  -u, --db-url URL             Set custom PostgreSQL database URL (optional)"
  echo "                               (format: *******************************)"
  echo "  -c, --cpu VALUE              Set TaskManager CPU value (optional)"
  echo "  --dry-run                    Show what would be changed without applying"
  echo "  -h, --help                   Display this help message"
  exit 1
}

# Function for logging
log() {
  local message="$1"
  local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] $message"
  echo "[$timestamp] $message" >> "$LOG_FILE"
}

# === Parse command line arguments ===
while [[ $# -gt 0 ]]; do
  case "$1" in
    -n|--namespace)
      NAMESPACE="$2"
      shift 2
      ;;
    -d|--deployment)
      FLINK_DEPLOYMENT_NAME="$2"
      shift 2
      ;;
    -p|--savepoint-path)
      BASE_SAVEPOINT_PATH="$2"
      shift 2
      ;;
    -u|--db-url)
      DB_URL="$2"
      shift 2
      ;;
    -c|--cpu)
      CPU_VALUE="$2"
      shift 2
      ;;
    --dry-run)
      DRY_RUN=true
      shift
      ;;
    -h|--help)
      usage
      ;;
    *)
      echo "Unknown option: $1"
      usage
      ;;
  esac
done

# === Validate required parameters ===
if [ -z "$NAMESPACE" ]; then
  NAMESPACE="str-int"  # Default namespace if not specified
  log "Using default namespace: $NAMESPACE"
fi

if [ -z "$FLINK_DEPLOYMENT_NAME" ]; then
  echo "Error: FlinkDeployment name is required"
  usage
fi

if [ -z "$BASE_SAVEPOINT_PATH" ]; then
  echo "Error: Savepoint base path is required"
  usage
fi

# Check if required tools are available
if ! command -v kubectl &> /dev/null; then
  echo "Error: kubectl is not installed or not in PATH"
  exit 1
fi

if ! command -v jq &> /dev/null; then
  echo "Error: jq is not installed or not in PATH"
  exit 1
fi

if ! command -v curl &> /dev/null; then
  echo "Error: curl is not installed or not in PATH"
  exit 1
fi

# Check if the specified FlinkDeployment exists
if ! kubectl get flinkdeployment "$FLINK_DEPLOYMENT_NAME" -n "$NAMESPACE" &>/dev/null; then
  log "Error: FlinkDeployment '$FLINK_DEPLOYMENT_NAME' not found in namespace '$NAMESPACE'"
  exit 1
fi

# === Config ===
TIMESTAMP=$(date +%s)
SAVEPOINT_DIR="${BASE_SAVEPOINT_PATH}/savepoint-${TIMESTAMP}"

log "Starting patch and restart with savepoint for FlinkDeployment '$FLINK_DEPLOYMENT_NAME' in namespace '$NAMESPACE'"
if [ "$DRY_RUN" = true ]; then
  log "Running in DRY-RUN mode - no changes will be applied"
fi

if [ -n "$DB_URL" ]; then
  log "Database URL will be updated to: $DB_URL"
fi

if [ -n "$CPU_VALUE" ]; then
  log "TaskManager CPU will be set to: $CPU_VALUE"
fi

# Ask for confirmation if not in dry-run mode
if [ "$DRY_RUN" = false ]; then
  echo ""
  echo "The following operations will be performed:"
  echo "1. Create a savepoint at: $SAVEPOINT_DIR"
  echo "2. Restart the FlinkDeployment from the savepoint"
  
  if [ -n "$CPU_VALUE" ]; then
    echo "3. Update TaskManager CPU to: $CPU_VALUE"
  fi
  
  if [ -n "$DB_URL" ]; then
    echo "4. Update Database URL to: $DB_URL"
  fi
  
  echo ""
  read -p "Do you want to proceed? (y/n): " CONFIRM
  if [[ ! "$CONFIRM" =~ ^[Yy]$ ]]; then
    log "Operation cancelled by user"
    exit 0
  fi
fi

# === Step 1: Port-forward JobManager REST endpoint ===
if [ "$DRY_RUN" = false ]; then
  log "🌐 Port-forwarding Flink JobManager REST API..."
  PORT_FORWARD_PID=""
  kubectl port-forward svc/${FLINK_DEPLOYMENT_NAME}-rest 8081:8081 -n "$NAMESPACE" >/dev/null 2>&1 &
  PORT_FORWARD_PID=$!
  sleep 3

  cleanup() {
    log "🧹 Cleaning up port-forward"
    kill "$PORT_FORWARD_PID" >/dev/null 2>&1 || true
  }
  trap cleanup EXIT
else
  log "[DRY-RUN] Would port-forward Flink JobManager REST API"
fi

# === Step 2: Get Job ID ===
if [ "$DRY_RUN" = false ]; then
  log "🔍 Fetching running job ID..."
  JOB_ID=$(curl -s http://localhost:8081/jobs/overview | jq -r '.jobs[] | select(.state=="RUNNING") | .jid')

  if [[ -z "$JOB_ID" ]]; then
    log "❌ No RUNNING job found for deployment: $FLINK_DEPLOYMENT_NAME"
    exit 1
  fi
  log "✅ Found running job: $JOB_ID"
else
  log "[DRY-RUN] Would fetch running job ID"
  JOB_ID="00000000000000000000000000000000"
  log "[DRY-RUN] Using placeholder job ID: $JOB_ID"
fi

# === Step 3: Trigger graceful stop with savepoint ===
if [ "$DRY_RUN" = false ]; then
  log "🛑 Stopping job with drain=true and savepoint path: $SAVEPOINT_DIR"
  STOP_RESPONSE=$(curl -s -X POST http://localhost:8081/jobs/${JOB_ID}/stop \
    -H "Content-Type: application/json" \
    -d "{\"drain\": true, \"target-directory\": \"${SAVEPOINT_DIR}\"}")

  TRIGGER_ID=$(echo "$STOP_RESPONSE" | jq -r '.["request-id"]')
                
  if [[ -z "$TRIGGER_ID" || "$TRIGGER_ID" == "null" ]]; then
    log "❌ Failed to trigger stop with savepoint."
    exit 1
  fi
  log "🚦 Stop triggered, trigger ID: $TRIGGER_ID."
else
  log "[DRY-RUN] Would stop job with savepoint path: $SAVEPOINT_DIR"
  TRIGGER_ID="mock-trigger-id"
  log "[DRY-RUN] Using placeholder trigger ID: $TRIGGER_ID"
fi

# === Step 4: Poll job status ===
if [ "$DRY_RUN" = false ]; then
  log "⏳ Waiting for job to stop..."
  for i in {1..30}; do
    JOB_STATE=$(curl -s http://localhost:8081/jobs/${JOB_ID} | jq -r '.state')

    if [[ "$JOB_STATE" == "FINISHED" || "$JOB_STATE" == "CANCELED" ]]; then
      log "✅ Job stopped with state: $JOB_STATE"
      break
    fi

    if [[ "$JOB_STATE" == "FAILED" ]]; then
      log "❌ Job failed during stop."
      exit 1
    fi

    sleep 5
  done

  NEW_SAVEPOINT_DIR=$(curl -s http://localhost:8081/jobs/${JOB_ID}/savepoints/${TRIGGER_ID} | jq -r '.operation.location')
  log "✅ Savepoint created at: $NEW_SAVEPOINT_DIR"
else
  log "[DRY-RUN] Would wait for job to stop and savepoint to be created"
  NEW_SAVEPOINT_DIR="$SAVEPOINT_DIR/mock-savepoint-path"
  log "[DRY-RUN] Using placeholder savepoint path: $NEW_SAVEPOINT_DIR"
fi

# === Step 5: Prepare patch for FlinkDeployment ===
NONCE=$(date +%s)
log "📦 Preparing patch for FlinkDeployment..."

# Start building the JSON patch
PATCH_OPERATIONS="[
{
  \"op\": \"replace\",
  \"path\": \"/spec/job/initialSavepointPath\",
  \"value\": \"$NEW_SAVEPOINT_DIR\"
},
{
  \"op\": \"replace\",
  \"path\": \"/spec/job/savepointRedeployNonce\",
  \"value\": $NONCE
},
{
  \"op\": \"replace\",
  \"path\": \"/spec/job/upgradeMode\",
  \"value\": \"last-state\"
}"

# Add CPU value if specified
if [ -n "$CPU_VALUE" ]; then
  PATCH_OPERATIONS="$PATCH_OPERATIONS,
{
  \"op\": \"replace\",
  \"path\": \"/spec/taskManager/resource/cpu\",
  \"value\": $CPU_VALUE
}"
fi

# Add DB_URL if specified
if [ -n "$DB_URL" ]; then
  # Get the container index that has the env vars
  CONTAINER_INDEX=$(kubectl get flinkdeployment "$FLINK_DEPLOYMENT_NAME" -n "$NAMESPACE" -o json |  
    jq '.spec.podTemplate.spec.containers | map(.name == "flink-main-container") | index(true) // 0')
  
  # Find the index of the existing DB_URL environment variable
  DB_URL_INDEX=$(kubectl get flinkdeployment "$FLINK_DEPLOYMENT_NAME" -n "$NAMESPACE" -o json | 
    jq ".spec.podTemplate.spec.containers[$CONTAINER_INDEX].env | map(.name == \"DB_URL\") | index(true)")
  
  if [ -z "$DB_URL_INDEX" ] || [ "$DB_URL_INDEX" == "null" ]; then
    log "Warning: DB_URL environment variable not found, will add it as a new variable"
    
    # Add DB_URL as a new env var
    PATCH_OPERATIONS="$PATCH_OPERATIONS,
{
  \"op\": \"add\",
  \"path\": \"/spec/podTemplate/spec/containers/$CONTAINER_INDEX/env/-\",
  \"value\": {
    \"name\": \"DB_URL\",
    \"value\": \"$DB_URL\"
  }
}"
  else
    log "Found DB_URL at index: $DB_URL_INDEX, will update existing variable"
    
    # Update existing DB_URL
    PATCH_OPERATIONS="$PATCH_OPERATIONS,
{
  \"op\": \"replace\",
  \"path\": \"/spec/podTemplate/spec/containers/$CONTAINER_INDEX/env/$DB_URL_INDEX\",
  \"value\": {
    \"name\": \"DB_URL\",
    \"value\": \"$DB_URL\"
  }
}"
  fi
fi

# Close the JSON patch array
PATCH_OPERATIONS="$PATCH_OPERATIONS
]"

# Create the final JSON patch
PATCH="$PATCH_OPERATIONS"

# Display the patch
log "Patch to be applied:"
echo "$PATCH" | jq '.' 2>/dev/null || echo "$PATCH"

read -p "Do you want to proceed? (y/n): " CONFIRM2

if [[ ! "$CONFIRM2" =~ ^[Yy]$ ]]; then
  log "Operation cancelled by user, patch not applied"
  exit 0
fi
# === Step 6: Apply the patch ===
if [ "$DRY_RUN" = false ]; then
  log "🔄 Applying patch to FlinkDeployment..."
  echo "$PATCH" | kubectl patch flinkdeployment "$FLINK_DEPLOYMENT_NAME" -n "$NAMESPACE" --type=json --patch-file=/dev/stdin
  
  if [ $? -ne 0 ]; then
    log "❌ Failed to patch FlinkDeployment."
    exit 1
  fi
  
  log "🚀 Restart requested from savepoint: $NEW_SAVEPOINT_DIR (savepointRedeployNonce: $NONCE)"
else
  log "[DRY-RUN] Would patch FlinkDeployment with the above changes"
fi

# === Step 7: Verify FlinkDeployment restart success ===
if [ "$DRY_RUN" = false ]; then
  log "⏳ Verifying FlinkDeployment restart..."

  # Set timeout parameters
  TIMEOUT_SECONDS=300  # 5 minutes
  POLL_INTERVAL=10     # Check every 10 seconds
  START_TIME=$(date +%s)
  END_TIME=$((START_TIME + TIMEOUT_SECONDS))

  # Poll until job is running or timeout is reached
  RESTART_SUCCESS=false
  while [ $(date +%s) -lt $END_TIME ]; do
    # Get current deployment status
    DEPLOYMENT_STATUS=$(kubectl get flinkdeployment $FLINK_DEPLOYMENT_NAME -n $NAMESPACE -o jsonpath='{.status.jobStatus.state}' 2>/dev/null)
    
    if [[ "$DEPLOYMENT_STATUS" == "RUNNING" ]]; then
      RESTART_SUCCESS=true
      log "✅ FlinkDeployment successfully restarted and is now RUNNING"
      break
    elif [[ "$DEPLOYMENT_STATUS" == "FAILED" ]]; then
      log "❌ FlinkDeployment restart failed - job is in FAILED state"
      
      # Get failure details if available
      FAILURE_MESSAGE=$(kubectl get flinkdeployment $FLINK_DEPLOYMENT_NAME -n $NAMESPACE -o jsonpath='{.status.jobStatus.error}' 2>/dev/null)
      if [[ -n "$FAILURE_MESSAGE" ]]; then
        log "📋 Failure details: $FAILURE_MESSAGE"
      fi
      
      break
    fi
    
    # Check for other potential error conditions in the deployment
    DEPLOYMENT_ERROR=$(kubectl get flinkdeployment $FLINK_DEPLOYMENT_NAME -n $NAMESPACE -o jsonpath='{.status.error}' 2>/dev/null)
    if [[ -n "$DEPLOYMENT_ERROR" && "$DEPLOYMENT_ERROR" != "null" ]]; then
      log "❌ Error detected in FlinkDeployment: $DEPLOYMENT_ERROR"
      break
    fi
    
    # Calculate elapsed time and remaining time
    CURRENT_TIME=$(date +%s)
    ELAPSED=$((CURRENT_TIME - START_TIME))
    REMAINING=$((TIMEOUT_SECONDS - ELAPSED))
    
    log "⏳ Current job state: $DEPLOYMENT_STATUS (Timeout in ${REMAINING}s)"
    sleep $POLL_INTERVAL
  done

  # Check if we timed out
  if [ $(date +%s) -ge $END_TIME ] && [ "$RESTART_SUCCESS" = false ]; then
    log "⏰ Timeout reached while waiting for FlinkDeployment to restart"
    
    # Get current pods status for debugging
    log "📋 Current pods status:"
    kubectl get pods -n $NAMESPACE | grep $FLINK_DEPLOYMENT_NAME | tee -a "$LOG_FILE"
    
    # Check JobManager logs for potential issues
    log "📋 Recent JobManager logs (if available):"
    JOBMANAGER_POD=$(kubectl get pods -n $NAMESPACE | grep $FLINK_DEPLOYMENT_NAME | grep jobmanager | awk '{print $1}' | head -1)
    if [[ -n "$JOBMANAGER_POD" ]]; then
      kubectl logs $JOBMANAGER_POD -n $NAMESPACE --tail=50 | tee -a "$LOG_FILE"
    else
      log "No JobManager pod found"
    fi
  fi

  # Final status summary
  log "=== Restart Operation Summary ==="
  log "FlinkDeployment: $FLINK_DEPLOYMENT_NAME"
  log "Namespace: $NAMESPACE"
  log "Savepoint path: $NEW_SAVEPOINT_DIR"
  
  if [ -n "$CPU_VALUE" ]; then
    log "TaskManager CPU: $CPU_VALUE"
  fi
  
  if [ -n "$DB_URL" ]; then
    log "Database URL: $DB_URL"
  fi
  
  log "Final state: $DEPLOYMENT_STATUS"

  if [ "$RESTART_SUCCESS" = true ]; then
    log "✅ Restart operation completed successfully"
    exit 0
  else
    log "❌ Restart operation did not complete successfully within the timeout period"
    exit 1
  fi
else
  log "[DRY-RUN] Would verify FlinkDeployment restart"
  log "[DRY-RUN] Operation completed successfully (dry run mode)"
fi
