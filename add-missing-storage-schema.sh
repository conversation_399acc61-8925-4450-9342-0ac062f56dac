#!/bin/bash
# Enhanced script to add STORAGE_SCHEMA_ID to FlinkDeployments and restart them
# This version combines both operations into a single atomic patch and adds
# optional savepoint functionality for graceful restarts

# Default values
NAMESPACE=""
DRY_RUN=false
LOG_FILE="storage-schema-update-$(date +%Y%m%d-%H%M%S).log"
USE_SAVEPOINT=false
SAVEPOINT_BASE_PATH=""
FLINK_REST_URL=""
PORT_FORWARD_PID=""

# Function to display usage information
usage() {
  echo "Usage: $0 [OPTIONS]"
  echo "Options:"
  echo "  -n, --namespace NAMESPACE    Specify the Kubernetes namespace (required)"
  echo "  -d, --dry-run                Show what would be changed without applying"
  echo "  -s, --savepoint              Enable savepoint functionality for graceful restarts"
  echo "  -p, --savepoint-path PATH    Base path for savepoint storage (required with -s)"
  echo "  -r, --rest-url URL           Public URL for Flink JobManager REST API"
  echo "                               (if not provided, port-forwarding will be used)"
  echo "  -h, --help                   Display this help message"
  exit 1
}

# Function for logging
log() {
  local message="$1"
  local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] $message"
  echo "[$timestamp] $message" >> "$LOG_FILE"
}

# Function to clean up port-forwarding
cleanup() {
  if [ -n "$PORT_FORWARD_PID" ]; then
    log "Cleaning up port-forward (PID: $PORT_FORWARD_PID)"
    kill "$PORT_FORWARD_PID" >/dev/null 2>&1 || true
  fi
}

# Set up trap for cleanup
trap cleanup EXIT

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    -n|--namespace)
      NAMESPACE="$2"
      shift 2
      ;;
    -d|--dry-run)
      DRY_RUN=true
      shift
      ;;
    -s|--savepoint)
      USE_SAVEPOINT=true
      shift
      ;;
    -p|--savepoint-path)
      SAVEPOINT_BASE_PATH="$2"
      shift 2
      ;;
    -r|--rest-url)
      FLINK_REST_URL="$2"
      shift 2
      ;;
    -h|--help)
      usage
      ;;
    *)
      echo "Unknown option: $1"
      usage
      ;;
  esac
done

# Validate required parameters
if [ -z "$NAMESPACE" ]; then
  echo "Error: Namespace is required"
  usage
fi

if [ "$USE_SAVEPOINT" = true ] && [ -z "$SAVEPOINT_BASE_PATH" ]; then
  echo "Error: Savepoint path is required when using savepoint functionality"
  usage
fi

# Check if required tools are available
if ! command -v kubectl &> /dev/null; then
  echo "Error: kubectl is not installed or not in PATH"
  exit 1
fi

if ! command -v jq &> /dev/null; then
  echo "Error: jq is not installed or not in PATH"
  exit 1
fi

if [ "$USE_SAVEPOINT" = true ] && ! command -v curl &> /dev/null; then
  echo "Error: curl is not installed or not in PATH (required for savepoint functionality)"
  exit 1
fi

log "Starting script to add STORAGE_SCHEMA_ID to FlinkDeployments in $NAMESPACE namespace"
if [ "$DRY_RUN" = true ]; then
  log "Running in DRY-RUN mode - no changes will be applied"
fi

if [ "$USE_SAVEPOINT" = true ]; then
  log "Savepoint functionality enabled - jobs will be gracefully stopped with savepoints"
  log "Savepoint base path: $SAVEPOINT_BASE_PATH"
  if [ -n "$FLINK_REST_URL" ]; then
    log "Using provided Flink REST API URL: $FLINK_REST_URL"
  else
    log "No REST API URL provided - will use port-forwarding"
  fi
fi

# Get all FlinkDeployments in the namespace
DEPLOYMENTS=$(kubectl get flinkdeployments -n "$NAMESPACE" -o jsonpath='{.items[*].metadata.name}' 2>/dev/null)
if [ $? -ne 0 ]; then
  log "Error: Failed to get FlinkDeployments in namespace $NAMESPACE"
  exit 1
fi

if [ -z "$DEPLOYMENTS" ]; then
  log "No FlinkDeployments found in namespace $NAMESPACE"
  exit 0
fi

# List all deployments and ask for confirmation
echo "Found the following FlinkDeployments in namespace $NAMESPACE:"
for DEPLOYMENT in $DEPLOYMENTS; do
  echo "- $DEPLOYMENT"
done

if [ "$DRY_RUN" = false ]; then
  read -p "Do you want to proceed with checking and patching these deployments? (y/n): " CONFIRM
  if [[ ! "$CONFIRM" =~ ^[Yy]$ ]]; then
    log "Operation cancelled by user"
    exit 0
  fi
fi

SUCCESSFUL_DEPLOYMENTS=0
FAILED_DEPLOYMENTS=0
SKIPPED_DEPLOYMENTS=0
SAVEPOINT_SUCCESSFUL=0
SAVEPOINT_FAILED=0

for DEPLOYMENT in $DEPLOYMENTS; do
  log "Checking $DEPLOYMENT..."
  
  # Check if STORAGE_SCHEMA_ID is missing
  HAS_STORAGE_SCHEMA_ID=$(kubectl get flinkdeployment "$DEPLOYMENT" -n "$NAMESPACE" -o json | 
    jq '.spec.podTemplate.spec.containers[].env | map(select(.name == "STORAGE_SCHEMA_ID")) | length')
  
  if [ "$HAS_STORAGE_SCHEMA_ID" -eq "0" ]; then
    log "STORAGE_SCHEMA_ID is missing in $DEPLOYMENT, extracting from ENRICHMENT_CONFIG..."
    
    # Extract schemaId from ENRICHMENT_CONFIG
    ENRICHMENT_CONFIG=$(kubectl get flinkdeployment "$DEPLOYMENT" -n "$NAMESPACE" -o json | 
      jq -r '.spec.podTemplate.spec.containers[].env[] | select(.name == "ENRICHMENT_CONFIG") | .value')
    
    if [ -n "$ENRICHMENT_CONFIG" ]; then
      # Extract the schemaId from the ENRICHMENT_CONFIG JSON
      SCHEMA_ID=$(echo "$ENRICHMENT_CONFIG" | jq -r '.[0].schemaId // empty')
      
      if [ -n "$SCHEMA_ID" ]; then
        log "Found schemaId: $SCHEMA_ID"
        
        # Get the container index that has the env vars
        CONTAINER_INDEX=$(kubectl get flinkdeployment "$DEPLOYMENT" -n "$NAMESPACE" -o json |  
          jq '.spec.podTemplate.spec.containers | map(.name == "flink-main-container") | index(true) // 0')
        
        # Generate a restartNonce value (timestamp-based)
        RESTART_NONCE=$(date +%s)
        
        # Ask for confirmation for each deployment if not in dry-run mode
        if [ "$DRY_RUN" = false ]; then
          if [ "$USE_SAVEPOINT" = true ]; then
            read -p "Create savepoint and patch $DEPLOYMENT with STORAGE_SCHEMA_ID? (y/n): " CONFIRM_PATCH
          else
            read -p "Patch $DEPLOYMENT with STORAGE_SCHEMA_ID and restart? (y/n): " CONFIRM_PATCH
          fi
          
          if [[ ! "$CONFIRM_PATCH" =~ ^[Yy]$ ]]; then
            log "Skipping $DEPLOYMENT by user request"
            ((SKIPPED_DEPLOYMENTS++))
            continue
          fi
          
          SAVEPOINT_PATH=""
          
          # Handle savepoint creation if enabled
          if [ "$USE_SAVEPOINT" = true ]; then
            log "Starting savepoint creation for $DEPLOYMENT..."
            TIMESTAMP=$(date +%s)
            SAVEPOINT_DIR="${SAVEPOINT_BASE_PATH}/${DEPLOYMENT}-savepoint-${TIMESTAMP}"
            log "Savepoint directory: $SAVEPOINT_DIR"
            
            # Set up REST API access
            REST_ENDPOINT=""
            if [ -n "$FLINK_REST_URL" ]; then
              REST_ENDPOINT="$FLINK_REST_URL"
            else
              log "Setting up port-forwarding for Flink JobManager REST API..."
              kubectl port-forward svc/${DEPLOYMENT}-rest 8081:8081 -n "$NAMESPACE" >/dev/null 2>&1 &
              PORT_FORWARD_PID=$!
              log "Port-forwarding started with PID: $PORT_FORWARD_PID"
              sleep 3
              REST_ENDPOINT="http://localhost:8081"
            fi
            
            if [ "$DRY_RUN" = false ]; then
              # Get Job ID
              log "Fetching running job ID..."
              JOB_ID=$(curl -s ${REST_ENDPOINT}/jobs/overview | jq -r '.jobs[] | select(.state=="RUNNING") | .jid')
              
              if [[ -z "$JOB_ID" ]]; then
                log "No RUNNING job found for deployment: $DEPLOYMENT"
                ((SAVEPOINT_FAILED++))
              else
                log "Found running job: $JOB_ID"
                
                # Trigger graceful stop with savepoint
                log "Stopping job with drain=true and savepoint path: $SAVEPOINT_DIR"
                STOP_RESPONSE=$(curl -s -X POST ${REST_ENDPOINT}/jobs/${JOB_ID}/stop \
                  -H "Content-Type: application/json" \
                  -d "{\"drain\": true, \"target-directory\": \"${SAVEPOINT_DIR}\"}")
                
                TRIGGER_ID=$(echo "$STOP_RESPONSE" | jq -r '.request-id')
                
                if [[ -z "$TRIGGER_ID" || "$TRIGGER_ID" == "null" ]]; then
                  log "Failed to trigger stop with savepoint."
                  ((SAVEPOINT_FAILED++))
                else
                  log "Stop triggered, trigger ID: $TRIGGER_ID"
                  
                  # Poll job status
                  log "Waiting for job to stop..."
                  JOB_STOPPED=false
                  for i in {1..30}; do
                    JOB_STATE=$(curl -s ${REST_ENDPOINT}/jobs/${JOB_ID} | jq -r '.state')
                    
                    if [[ "$JOB_STATE" == "FINISHED" || "$JOB_STATE" == "CANCELED" ]]; then
                      log "Job stopped with state: $JOB_STATE"
                      JOB_STOPPED=true
                      SAVEPOINT_PATH="$SAVEPOINT_DIR"
                      ((SAVEPOINT_SUCCESSFUL++))
                      break
                    fi
                    
                    if [[ "$JOB_STATE" == "FAILED" ]]; then
                      log "Job failed during stop."
                      ((SAVEPOINT_FAILED++))
                      break
                    fi
                    
                    log "Job state: $JOB_STATE, waiting..."
                    sleep 5
                  done
                  
                  if [ "$JOB_STOPPED" = false ]; then
                    log "Timed out waiting for job to stop."
                    ((SAVEPOINT_FAILED++))
                  fi
                fi
              fi
              
              # Clean up port-forwarding if we set it up
              if [ -z "$FLINK_REST_URL" ] && [ -n "$PORT_FORWARD_PID" ]; then
                kill "$PORT_FORWARD_PID" >/dev/null 2>&1 || true
                PORT_FORWARD_PID=""
                log "Port-forwarding stopped"
              fi
            else
              log "[DRY-RUN] Would create savepoint at: $SAVEPOINT_DIR"
              SAVEPOINT_PATH="$SAVEPOINT_DIR"
            fi
          fi
          
          # Create the appropriate patch based on whether we have a savepoint
          if [ -n "$SAVEPOINT_PATH" ]; then
            # Create a combined JSON patch that adds the new env var, updates restartNonce, and sets savepoint path
            PATCH=$(cat <<EOF
[
  {
    "op": "add",
    "path": "/spec/podTemplate/spec/containers/$CONTAINER_INDEX/env/-",
    "value": {
      "name": "STORAGE_SCHEMA_ID",
      "value": "$SCHEMA_ID"
    }
  },
  {
    "op": "replace",
    "path": "/spec/restartNonce",
    "value": $RESTART_NONCE
  },
  {
    "op": "replace",
    "path": "/spec/job/initialSavepointPath",
    "value": "$SAVEPOINT_PATH"
  }
]
EOF
)
          else
            # Create a combined JSON patch that just adds the new env var and updates restartNonce
            PATCH=$(cat <<EOF
[
  {
    "op": "add",
    "path": "/spec/podTemplate/spec/containers/$CONTAINER_INDEX/env/-",
    "value": {
      "name": "STORAGE_SCHEMA_ID",
      "value": "$SCHEMA_ID"
    }
  },
  {
    "op": "replace",
    "path": "/spec/restartNonce",
    "value": $RESTART_NONCE
  }
]
EOF
)
          fi
          
          # Display the patch
          log "Patch to be applied:"
          echo "$PATCH"
          
          if [ "$DRY_RUN" = false ]; then
            # Apply the JSON patch
            if [ -n "$SAVEPOINT_PATH" ]; then
              log "Applying patch to add STORAGE_SCHEMA_ID=$SCHEMA_ID, restart with nonce=$RESTART_NONCE, and use savepoint=$SAVEPOINT_PATH"
            else
              log "Applying patch to add STORAGE_SCHEMA_ID=$SCHEMA_ID and restart with nonce=$RESTART_NONCE"
            fi
            
            echo "$PATCH" | kubectl patch flinkdeployment "$DEPLOYMENT" -n "$NAMESPACE" --type=json --patch-file=/dev/stdin
            
            if [ $? -eq 0 ]; then
              if [ -n "$SAVEPOINT_PATH" ]; then
                log "Successfully patched $DEPLOYMENT with STORAGE_SCHEMA_ID and triggered restart from savepoint"
              else
                log "Successfully patched $DEPLOYMENT with STORAGE_SCHEMA_ID and triggered restart"
              fi
              ((SUCCESSFUL_DEPLOYMENTS++))
            else
              log "Failed to patch $DEPLOYMENT"
              ((FAILED_DEPLOYMENTS++))
            fi
          else
            if [ -n "$SAVEPOINT_PATH" ]; then
              log "[DRY-RUN] Would patch $DEPLOYMENT with STORAGE_SCHEMA_ID and restart from savepoint"
            else
              log "[DRY-RUN] Would patch $DEPLOYMENT with STORAGE_SCHEMA_ID and restart"
            fi
          fi
        else
          # Dry-run mode
          if [ "$USE_SAVEPOINT" = true ]; then
            TIMESTAMP=$(date +%s)
            SAVEPOINT_DIR="${SAVEPOINT_BASE_PATH}/${DEPLOYMENT}-savepoint-${TIMESTAMP}"
            log "[DRY-RUN] Would create savepoint at: $SAVEPOINT_DIR"
            log "[DRY-RUN] Would add STORAGE_SCHEMA_ID=$SCHEMA_ID, update restartNonce to $RESTART_NONCE, and use savepoint"
          else
            log "[DRY-RUN] Would add STORAGE_SCHEMA_ID=$SCHEMA_ID and update restartNonce to $RESTART_NONCE"
          fi
        fi
      else
        log "Error: Could not extract schemaId from ENRICHMENT_CONFIG in $DEPLOYMENT"
        ((FAILED_DEPLOYMENTS++))
      fi
    else
      log "Error: ENRICHMENT_CONFIG not found in $DEPLOYMENT"
      ((FAILED_DEPLOYMENTS++))
    fi
  else
    log "STORAGE_SCHEMA_ID already exists in $DEPLOYMENT, skipping"
    ((SKIPPED_DEPLOYMENTS++))
  fi
  
  log "-----------------------------------"
done

# Summary
log "Operation completed"
log "Summary:"
log "- Total deployments processed: $((SUCCESSFUL_DEPLOYMENTS + FAILED_DEPLOYMENTS + SKIPPED_DEPLOYMENTS))"
log "- Successfully patched and restarted: $SUCCESSFUL_DEPLOYMENTS"
log "- Failed to patch: $FAILED_DEPLOYMENTS"
log "- Skipped (already had STORAGE_SCHEMA_ID or user skipped): $SKIPPED_DEPLOYMENTS"

if [ "$USE_SAVEPOINT" = true ]; then
  log "- Savepoints successfully created: $SAVEPOINT_SUCCESSFUL"
  log "- Savepoint creation failed: $SAVEPOINT_FAILED"
fi

log "Log file: $LOG_FILE"

if [ "$DRY_RUN" = true ]; then
  log "This was a dry run. No changes were applied."
fi

echo "Done!"
