apiVersion: v1
kind: ConfigMap
metadata:
  name: confluent-proxy-conf
data: 
  nginx.conf: |
    events {}
    stream {
      map $ssl_preread_server_name $targetBackend {
        default $ssl_preread_server_name;
    }

    server {
      listen 9092;

      proxy_connect_timeout 1s;
      proxy_timeout 7200s;

      resolver *************;

      proxy_pass $targetBackend:9092;
      ssl_preread on;
    }

    server {
      listen 443;

      proxy_connect_timeout 1s;
      proxy_timeout 7200s;

      resolver *************;

      proxy_pass $targetBackend:443;
      ssl_preread on;
    }

    log_format stream_routing '[$time_local] remote address $remote_addr'
                        'with SNI name "$ssl_preread_server_name" '
                        'proxied to "$upstream_addr" '
                        '$protocol $status $bytes_sent $bytes_received '
                        '$session_time';
    access_log /var/log/nginx/stream-access.log stream_routing;
    }
