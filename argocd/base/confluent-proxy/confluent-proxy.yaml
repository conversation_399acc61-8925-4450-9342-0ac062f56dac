apiVersion: apps/v1
kind: Deployment
metadata:
  name: confluent-proxy
  labels:
    app.kubernetes.io/name: confluent-proxy
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: confluent-proxy
  template:
    metadata:
      labels:
        app.kubernetes.io/name: confluent-proxy        
    spec:
      containers:
      - name: nginx
        image: nginx:1.25.4-alpine3.18
        resources:
          requests:
            memory: "100Mi"
            cpu: "100m"
          limits:
            memory: "150Mi"
            cpu: "100m"
        securityContext:
          allowPrivilegeEscalation: false
        ports:
          - containerPort: 80
        volumeMounts:
          - mountPath: "/etc/nginx"
            name: confluent-proxy-conf
      volumes:
      - name: confluent-proxy-conf
        configMap:
          name: confluent-proxy-conf
