apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql-proxy
  labels:
    app.kubernetes.io/name: postgresql-proxy
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: postgresql-proxy
  template:
    metadata:
      labels:
        app.kubernetes.io/name: postgresql-proxy        
    spec:
      containers:
      - name: postgresql-proxy
        image: alpine/socat:1.8.0.0
        args:
          - tcp-listen:5432,fork,reuseaddr
          - tcp-connect:ude-str-dev-eu-postgres.postgres.database.azure.com:5432
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        ports:
          - containerPort: 5432
        securityContext:
          allowPrivilegeEscalation: false
