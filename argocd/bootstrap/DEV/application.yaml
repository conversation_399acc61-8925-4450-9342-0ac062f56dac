apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: str-dev-root
spec:
  project: str-dev
  source:
    path: argoCD/applications/DEV # it watches over the microservices directory
    repoURL: **************:cariad-ude/ude-str-stream-processing-flink.git # always put the .git extension here
    targetRevision: HEAD
    directory:
      recurse: false
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: str-dev
  syncPolicy:
    syncOptions:
    - CreateNamespace=false  
    automated:
      selfHeal: false