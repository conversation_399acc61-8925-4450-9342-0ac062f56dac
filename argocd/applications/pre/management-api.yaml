apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: management-api
  namespace: str-pre
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-management-api.git
        revision: HEAD
        files:
          - path: helm/management-api/values-pre-eu.yaml
  template:
    metadata:
      name: 'management-api'
    spec:
      project: str-pre
      source:
        path: ''
        repoURL: crmegahubwesteurope.azurecr.io/str-pre/helm
        targetRevision: '{{ .image.tag }}'
        chart: management-api
        helm:
          valueFiles:
            - values-pre-eu.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: str-pre
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
