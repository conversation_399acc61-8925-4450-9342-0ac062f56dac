apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: data-ingestion-api
  namespace: str-pre
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-data-ingestion-api.git
        revision: HEAD
        files:
          - path: helm/data-ingestion-api/values-pre-eu.yaml
  template:
    metadata:
      name: 'data-ingestion-api'
    spec:
      project: str-pre
      source:
        path: ''
        repoURL: crmegahubwesteurope.azurecr.io/str-pre/helm
        targetRevision: '{{ .image.tag }}'
        chart: data-ingestion-api
        helm:
          valueFiles:
            - values-pre-eu.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: str-pre
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
