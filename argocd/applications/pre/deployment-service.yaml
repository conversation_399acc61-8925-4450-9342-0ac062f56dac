apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: deployment-service
  namespace: str-pre

spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-deployment-service.git
        revision: HEAD
        files:
          - path: helm/deployment-service/values-pre-eu.yaml
  template:
    metadata:
      name: 'deployment-service'
    spec:
      project: str-pre
      source:
        path: ''
        repoURL: crmegahubwesteurope.azurecr.io/str-pre/helm
        targetRevision: '{{ .image.tag }}'
        chart: deployment-service
        helm:
          valueFiles:
            - values-pre-eu.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: str-pre
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
