apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: signal-definition-retrieval-service
  namespace: str-pre
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-signal-definition-retrieval-service.git
        revision: HEAD
        files:
          - path: helm/signal-definition-retrieval-service/values-pre-eu.yaml
  template:
    metadata:
      name: 'signal-definition-retrieval-service'
    spec:
      project: str-pre
      source:
        path: ''
        repoURL: crmegahubwesteurope.azurecr.io/str-pre/helm
        targetRevision: '{{ .image.tag }}'
        chart: signal-definition-retrieval-service
        helm:
          valueFiles:
            - values-pre-eu.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: str-pre
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
