apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: flink-playground
  namespace: str-pre
spec:
  project: str-pre
  source:
    path: argocd/overlays/flink-playground/pre # it watches over the microservices directory
    repoURL: https://github.com/cariad-ude/ude-str-argocd-applications.git # always put the .git extension here
    targetRevision: HEAD
    directory:
      recurse: false
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: str-pre
  syncPolicy:
    syncOptions:
    - CreateNamespace=false  
    automated:
      selfHeal: false
