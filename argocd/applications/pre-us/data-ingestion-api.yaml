apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: data-ingestion-api
  namespace: udestrus-pre
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-data-ingestion-api.git
        revision: HEAD
        files:
          - path: helm/data-ingestion-api/values-pre-us.yaml
  template:
    metadata:
      name: 'data-ingestion-api'
    spec:
      project: udestrus-pre
      source:
        path: ''
        repoURL: crmegahubeastus2.azurecr.io/udestrus-pre/helm
        targetRevision: '{{ .image.tag }}'
        chart: data-ingestion-api
        helm:
          valueFiles:
            - values-pre-us.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: udestrus-pre
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
