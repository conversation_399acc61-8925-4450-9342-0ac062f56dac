apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: deployment-service
  namespace: udestrus-pre

spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-deployment-service.git
        revision: HEAD
        files:
          - path: helm/deployment-service/values-pre-us.yaml
  template:
    metadata:
      name: 'deployment-service'
    spec:
      project: udestrus-pre
      source:
        path: ''
        repoURL: crmegahubeastus2.azurecr.io/udestrus-pre/helm
        targetRevision: '{{ .image.tag }}'
        chart: deployment-service
        helm:
          valueFiles:
            - values-pre-us.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: udestrus-pre
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
