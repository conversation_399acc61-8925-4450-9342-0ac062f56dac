apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: data-protection-service
  namespace: str-int
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-data-protection-service.git
        revision: HEAD
        files:
          - path: helm/data-protection-service/values-int.yaml
  template:
    metadata:
      name: 'data-protection-service'
    spec:
      project: str-int
      source:
        path: ''
        repoURL: crmegahubwesteurope.azurecr.io/str-int/helm
        targetRevision: '{{ .image.tag }}'
        chart: data-protection-service
        helm:
          valueFiles:
            - values-int.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: str-int
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
