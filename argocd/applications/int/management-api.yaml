apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: management-api
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-management-api.git
        revision: HEAD
        files:
          - path: helm/management-api/values-int.yaml
  template:
    metadata:
      name: 'management-api'
    spec:
      project: str-int
      source:
        path: ''
        repoURL: crmegahubwesteurope.azurecr.io/str-int/helm
        targetRevision: '{{ .image.tag }}'
        chart: management-api
        helm:
          valueFiles:
            - values-int.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: str-int
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
