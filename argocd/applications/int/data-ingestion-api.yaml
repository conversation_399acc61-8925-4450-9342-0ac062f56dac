apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: data-ingestion-api
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-data-ingestion-api.git
        revision: HEAD
        files:
          - path: helm/data-ingestion-api/values-int.yaml
  template:
    metadata:
      name: 'data-ingestion-api'
    spec:
      project: str-int
      source:
        path: ''
        repoURL: crmegahubwesteurope.azurecr.io/str-int/helm
        targetRevision: '{{ .image.tag }}'
        chart: data-ingestion-api
        helm:
          valueFiles:
            - values-int.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: str-int
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
