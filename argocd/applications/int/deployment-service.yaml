apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: deployment-service
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-deployment-service.git
        revision: HEAD
        files:
          - path: helm/deployment-service/values-int.yaml
  template:
    metadata:
      name: 'deployment-service'
    spec:
      project: str-int
      source:
        path: ''
        repoURL: crmegahubwesteurope.azurecr.io/str-int/helm
        targetRevision: '{{ .image.tag }}'
        chart: deployment-service
        helm:
          valueFiles:
            - values-int.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: str-int
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
