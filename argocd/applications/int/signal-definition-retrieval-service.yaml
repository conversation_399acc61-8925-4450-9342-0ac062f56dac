apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: signal-definition-retrieval-service
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-signal-definition-retrieval-service.git
        revision: HEAD
        files:
          - path: helm/signal-definition-retrieval-service/values-int.yaml
  template:
    metadata:
      name: 'signal-definition-retrieval-service'
    spec:
      project: str-int
      source:
        path: ''
        repoURL: crmegahubwesteurope.azurecr.io/str-int/helm
        targetRevision: '{{ .image.tag }}'
        chart: signal-definition-retrieval-service
        helm:
          valueFiles:
            - values-int.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: str-int
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
