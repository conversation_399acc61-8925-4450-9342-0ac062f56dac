apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: data-protection-service
  namespace: str-dev
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-data-protection-service.git
        revision: HEAD
        files:
          - path: helm/data-protection-service/values-dev.yaml
  template:
    metadata:
      name: 'data-protection-service'
    spec:
      project: str-dev
      source:
        path: ''
        repoURL: crmegahubwesteurope.azurecr.io/str-dev/helm
        targetRevision: '{{ .image.tag }}-{{ .image.commitSha }}'
        chart: data-protection-service
        helm:
          valueFiles:
            - values-dev.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: str-dev
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
