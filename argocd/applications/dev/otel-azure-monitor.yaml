apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: otel-azure-monitor
spec:
  project: str-dev
  source:
    path: argocd/overlays/otel-azure-monitor/dev # it watches over the microservices directory
    repoURL: https://github.com/cariad-ude/ude-str-argocd-applications.git # always put the .git extension here
    targetRevision: HEAD
    directory:
      recurse: false
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: str-dev
  syncPolicy:
    syncOptions:
    - CreateNamespace=false  
    automated:
      selfHeal: false
