apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: signal-definition-retrieval-service
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-signal-definition-retrieval-service.git
        revision: HEAD
        files:
          - path: helm/signal-definition-retrieval-service/values-dev.yaml
  template:
    metadata:
      name: 'signal-definition-retrieval-service'
    spec:
      project: str-dev
      source:
        path: ''
        repoURL: crmegahubwesteurope.azurecr.io/str-dev/helm
        targetRevision: '{{ .image.tag }}-{{ .image.commitSha }}'
        chart: signal-definition-retrieval-service
        helm:
          valueFiles:
            - values-dev.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: str-dev
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
