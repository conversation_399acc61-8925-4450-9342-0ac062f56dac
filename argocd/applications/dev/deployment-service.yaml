apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: deployment-service
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-deployment-service.git
        revision: HEAD
        files:
          - path: helm/deployment-service/values-dev.yaml
  template:
    metadata:
      name: 'deployment-service'
    spec:
      project: str-dev
      source:
        path: ''
        repoURL: crmegahubwesteurope.azurecr.io/str-dev/helm
        targetRevision: '{{ .image.tag }}-{{ .image.commitSha }}'
        chart: deployment-service
        helm:
          valueFiles:
            - values-dev.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: str-dev
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
