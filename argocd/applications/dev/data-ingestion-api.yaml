apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: data-ingestion-api
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-data-ingestion-api.git
        revision: HEAD
        files:
          - path: helm/data-ingestion-api/values-dev.yaml
  template:
    metadata:
      name: 'data-ingestion-api'
    spec:
      project: str-dev
      source:
        path: ''
        repoURL: crmegahubwesteurope.azurecr.io/str-dev/helm
        targetRevision: '{{ .image.tag }}-{{ .image.commitSha }}'
        chart: data-ingestion-api
        helm:
          valueFiles:
            - values-dev.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: str-dev
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
