apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: signal-definition-retrieval-service
  namespace: str-prd
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-signal-definition-retrieval-service.git
        revision: HEAD
        files:
          - path: helm/signal-definition-retrieval-service/values-prd-eu.yaml
  template:
    metadata:
      name: 'signal-definition-retrieval-service'
    spec:
      project: str-prd
      source:
        path: ''
        repoURL: crmegahubwesteurope.azurecr.io/str-prd/helm
        targetRevision: '{{ .image.tag }}'
        chart: signal-definition-retrieval-service
        helm:
          valueFiles:
            - values-prd-eu.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: str-prd
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
