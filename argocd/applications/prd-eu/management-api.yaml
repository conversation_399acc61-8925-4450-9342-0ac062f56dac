apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: management-api
  namespace: str-prd
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-management-api.git
        revision: HEAD
        files:
          - path: helm/management-api/values-prd-eu.yaml
  template:
    metadata:
      name: 'management-api'
    spec:
      project: str-prd
      source:
        path: ''
        repoURL: crmegahubwesteurope.azurecr.io/str-prd/helm
        targetRevision: '{{ .image.tag }}'
        chart: management-api
        helm:
          valueFiles:
            - values-prd-eu.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: str-prd
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
