apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: data-ingestion-api
  namespace: str-prd
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-data-ingestion-api.git
        revision: HEAD
        files:
          - path: helm/data-ingestion-api/values-prd-eu.yaml
  template:
    metadata:
      name: 'data-ingestion-api'
    spec:
      project: str-prd
      source:
        path: ''
        repoURL: crmegahubwesteurope.azurecr.io/str-prd/helm
        targetRevision: '{{ .image.tag }}'
        chart: data-ingestion-api
        helm:
          valueFiles:
            - values-prd-eu.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: str-prd
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
