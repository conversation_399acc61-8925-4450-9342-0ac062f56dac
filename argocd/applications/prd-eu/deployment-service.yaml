apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: deployment-service
  namespace: str-prd

spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-deployment-service.git
        revision: HEAD
        files:
          - path: helm/deployment-service/values-prd-eu.yaml
  template:
    metadata:
      name: 'deployment-service'
    spec:
      project: str-prd
      source:
        path: ''
        repoURL: crmegahubwesteurope.azurecr.io/str-prd/helm
        targetRevision: '{{ .image.tag }}'
        chart: deployment-service
        helm:
          valueFiles:
            - values-prd-eu.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: str-prd
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
