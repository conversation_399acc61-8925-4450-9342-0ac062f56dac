apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: deployment-service
  namespace: udestrus-prd

spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-deployment-service.git
        revision: HEAD
        files:
          - path: helm/deployment-service/values-prd-us.yaml
  template:
    metadata:
      name: 'deployment-service'
    spec:
      project: udestrus-prd
      source:
        path: ''
        repoURL: crmegahubeastus2.azurecr.io/udestrus-prd/helm
        targetRevision: '{{ .image.tag }}'
        chart: deployment-service
        helm:
          valueFiles:
            - values-prd-us.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: udestrus-prd
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
