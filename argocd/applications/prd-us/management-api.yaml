apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: management-api
  namespace: udestrus-prd
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-management-api.git
        revision: HEAD
        files:
          - path: helm/management-api/values-prd-us.yaml
  template:
    metadata:
      name: 'management-api'
    spec:
      project: udestrus-prd
      source:
        path: ''
        repoURL: crmegahubeastus2.azurecr.io/udestrus-prd/helm
        targetRevision: '{{ .image.tag }}'
        chart: management-api
        helm:
          valueFiles:
            - values-prd-us.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: udestrus-prd
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
