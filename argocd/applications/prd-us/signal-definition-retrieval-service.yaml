apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: signal-definition-retrieval-service
  namespace: udestrus-prd
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - git:
        repoURL: https://github.com/cariad-ude/ude-str-signal-definition-retrieval-service.git
        revision: HEAD
        files:
          - path: helm/signal-definition-retrieval-service/values-prd-us.yaml
  template:
    metadata:
      name: 'signal-definition-retrieval-service'
    spec:
      project: udestrus-prd
      source:
        path: ''
        repoURL: crmegahubeastus2.azurecr.io/udestrus-prd/helm
        targetRevision: '{{ .image.tag }}'
        chart: signal-definition-retrieval-service
        helm:
          valueFiles:
            - values-prd-us.yaml
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: udestrus-prd
      syncPolicy:
        automated: {}
        syncOptions:
          - CreateNamespace=false
