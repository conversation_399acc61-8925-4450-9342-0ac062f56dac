apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: otel-azure-monitor
  namespace: udestrus-prd
spec:
  project: udestrus-prd
  source:
    path: argocd/overlays/otel-azure-monitor/prd-us # it watches over the microservices directory
    repoURL: https://github.com/cariad-ude/ude-str-argocd-applications.git # always put the .git extension here
    targetRevision: HEAD
    directory:
      recurse: false
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: udestrus-prd
  syncPolicy:
    syncOptions:
    - CreateNamespace=false  
    automated:
      selfHeal: false
