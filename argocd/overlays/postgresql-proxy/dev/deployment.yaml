apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql-proxy
  labels:
    app.kubernetes.io/name: postgresql-proxy
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: postgresql-proxy
  template:
    metadata:
      labels:
        app.kubernetes.io/name: postgresql-proxy        
    spec:
      containers:
      - name: postgresql-proxy
        image: crmegahubwesteurope.azurecr.io/docker.io/alpine/socat:1.8.0.0
        args:
          - tcp-listen:5432,fork,reuseaddr
          - tcp-connect:ude-str-dev-eu-postgres-wealthy-osprey.postgres.database.azure.com:5432
        resources:
          requests:
            memory: "115Mi"
            cpu: "100m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        ports:
          - containerPort: 5432
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
