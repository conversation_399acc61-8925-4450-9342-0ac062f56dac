apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: udestrus-pre

# Patches the base/existing manifests
patches:
- path: secretstore.yaml
- path: roles.yaml

# Applies new/overlay specific manifests
resources:
- ../../../base/flink-playground
- pdb.yaml
# - flink-gdc-job.yaml
- flink-gdc-job-schema-registry-secret.yaml
- flink-gdc-job-kafka-api-secret.yaml
- flink-gdc-job-postgresql-secret.yaml
- flink-gdc-job-azure-blob-secret.yaml
- flink-basic-auth-external-secret.yaml
- flinkdeployment-gdc-pipeline-application-mode.yaml
- flinkdeployment-passthrough-application-mode.yaml
- flinkdeployment-vms-application-mode.yaml
- team_secretstore.yaml