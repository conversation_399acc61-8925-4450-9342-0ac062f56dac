apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: flink-vms-application-mode
spec:
  image: crmegahubeastus2.azurecr.io/udestrus-pre/vms-postgres-job:f120-1.0-SNAPSHOT
  flinkVersion: v1_20
  mode: native
  podTemplate:
    metadata:
      labels:
        app.kubernetes.io/name: "flink-vms-application-mode"
    spec:
      containers:
        - name: flink-main-container
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
            runAsNonRoot: true
            runAsUser: 1000
          env:
            - name: env.java.opts.all
              value: "--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED"
            - name: BOOTSTRAP_SERVERS
              value: "pkc-5wkokg.eastus2.azure.confluent.cloud:9092"
            - name: KAFKA_CLUSTER_API_SECRET
              valueFrom:
                secretKeyRef:
                  name: ude-str-flink-gdc-job-kafka-api-secret
                  key: confluent_kafka_api_secret
            - name: KAFKA_CLUSTER_API_KEY
              valueFrom:
                secretKeyRef:
                  name: ude-str-flink-gdc-job-kafka-api-secret
                  key: confluent_kafka_api_key
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: ude-str-flink-gdc-job-db-password
                  key: postgresql_admin_password
            - name: AZURE_CREDENTIAL_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: ude-str-flink-store-and-explore-spn-secret
                  key: azure_credential_client_secret
            - name: KAFKA_VMS_SOURCE_TOPIC_NAME
              value: "private.ude-str.vms-connector.flattened.json"
            - name: FLINK_GROUP_ID
              value: "flink-vms-job"
            - name: DB_URL
              value: "************************************************************************************"
            - name: DB_USER_NAME
              value: "psqladmin"
            - name: DB_VMS_DATABASE_NAME
              value: "vms"
            - name: DB_VMS_VEHICLE_DATA_TABLE_NAME
              value: "vehicle_data"
            - name: HADOOP_OPTIONAL_TOOLS
              value: hadoop-azure
            #- name: AZURE_BLOB_ENDPOINT
            #  value: "https://fixme.blob.core.windows.net" # TODO invalid and MUST be replaced (ask Alex K for real value)
            #- name: AZURE_CREDENTIAL_TENANT_ID
            #  value: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
            #- name: AZURE_CREDENTIAL_CLIENT_ID
            #  value: "1528f445-2a68-4c4d-a914-4fbac9be6759" # TODO invalid and MUST be replaced
          volumeMounts:
            - mountPath: /opt/flink/log
              name: flink-logs
            - mountPath: /opt/flink/downloads
              name: downloads
      volumes:
        - name: flink-logs
          emptyDir: { }
        - name: downloads
          emptyDir: { }

  flinkConfiguration:
    kubernetes.jobmanager.annotations: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/
    kubernetes.taskmanager.annotations: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/
    classloader.resolve-order: parent-first
    taskmanager.numberOfTaskSlots: "2"
    #jobmanager.heap.size: ""
    jobmanager.memory.process.size: "1GB"
    #taskmanager.heap.size: ""

    #taskmanager.memory.task.heap.size: "1GB"
    taskmanager.memory.process.size: "1GB"
    taskmanager.memory.managed.fraction: "0.1"
    env.java.opts.all: "--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED"
    job.autoscaler.enabled: "true"
    job.autoscaler.memory.tuning.enabled: "true"
    # Enable Adaptvie scheduler to play the in-place rescaling.
    jobmanager.scheduler: adaptive
    job.autoscaler.memory.tuning.overhead: "1"
    # Enable autoscale and scaling
    job.autoscaler.scaling.enabled: "true"
    job.autoscaler.stabilization.interval: 1m
    job.autoscaler.metrics.window: 1m
    # Enable flame graph for statistics !!!! Only for dev and pre-prod envs !!!!
    rest.flamegraph.enabled: "true"
    rest.flamegraph.refresh-interval: "10 m"
    rest.profiling.enabled: "true"

    metrics.reporter.prom.factory.class: org.apache.flink.metrics.prometheus.PrometheusReporterFactory
    metrics.reporter.prom.port: "9249"
    kubernetes.container.image.pull-secrets: mega-container-registry-secret

    # Flink State Backend, Storage account access key
    #fs.azure.account.auth.type.udestrdeveuflinkstorage.blob.core.windows.net: SharedKey
    #fs.azure.account.keyprovider.udestrpreusflinkstorage.blob.core.windows.net: org.apache.flink.fs.azurefs.EnvironmentVariableKeyProvider
    #fs.azure.account.key.udestrdeveuflinkstorage.dfs.core.windows.net: $AZURE_STORAGE_KEY

    # Checkpointing
    #execution.checkpointing.dir: wasbs://<EMAIL>/flinkVmsJob/checkpoints
    #state.checkpoints.dir: wasbs://<EMAIL>/flinkVmsJob/checkpoints
    #execution.checkpointing.interval: "20 min"
    #execution.checkpointing.mode: "EXACTLY_ONCE"
    #execution.checkpointing.timeout: "10 min"
    #execution.checkpointing.min-pause: "2 min"
    #execution.checkpointing.max-concurrent-checkpoints: "1"

    #execution.checkpointing.storage: filesystem
    # Increase incremental checkpointing to avoid huge size checkpointing
    #execution.checkpointing.incremental: "true"

    # # RocksDB-Specific Tuning
    state.backend.type: rocksdb
    fs.azure.data.blocks.buffer: array

  ingress:
    template: "udestrus-pre.apps.mega.cariad.cloud/{{name}}(/|$)(.*)"
    className: "nginx"
    annotations:
      nginx.ingress.kubernetes.io/auth-type: basic
      nginx.ingress.kubernetes.io/auth-secret: ude-str-flink-basic-auth
      nginx.ingress.kubernetes.io/auth-realm: "Authentication Required - Flink Playground"
      nginx.ingress.kubernetes.io/rewrite-target: "/$2"
  jobManager:
    resource:
      memory: "2Gi"
      cpu: 1
  taskManager:
    resource:
      memory: "2Gi"
      cpu: 1
  serviceAccount: flink
  job:
    jarURI: local:///opt/flink/lib/flink-job-all.jar
    entryClass: org.ude.VmsConnectorJob
    parallelism: 1
    upgradeMode: stateless
  logConfiguration:
    "logback-console.xml": |
      <configuration>
        <property name="pattern" value="%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread) %cyan(%-12.36logger{36}) %msg%n" />
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.out</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>ERROR</level>
                <onMatch>DENY</onMatch>
            </filter>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>WARN</level>
                <onMatch>DENY</onMatch>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <appender name="STDERR" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.err</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>WARN</level>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <root level="INFO">
            <appender-ref ref="STDOUT" />
            <appender-ref ref="STDERR" />
        </root>

        <!-- Specific loggers -->
        <logger name="akka" level="INFO"/>
        <logger name="org.apache.kafka" level="INFO"/>
        <logger name="org.apache.hadoop" level="INFO"/>
        <logger name="org.apache.zookeeper" level="INFO"/>
        <logger name="org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline" level="OFF"/>
      </configuration>
