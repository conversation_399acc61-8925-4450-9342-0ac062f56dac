apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: flink-gdc-application-mode

spec:
  image: crmegahubwesteurope.azurecr.io/str-dev/gdc-job:1.0-SNAPSHOT
  imagePullPolicy: Always
  flinkVersion: v1_20
  mode: native
  podTemplate:
    metadata:
      labels:
        app.kubernetes.io/name: "flink-gdc-application-mode"
        azure.workload.identity/use: "true"
    spec:
      containers:
        - name: flink-main-container
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
            runAsNonRoot: true
            runAsUser: 9999
          env:
            - name: env.java.opts.all
              value: "--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED"
            - name: BOOTSTRAP_SERVERS
              value: "pkc-09081p.westeurope.azure.confluent.cloud:9092"
            - name: SCHEMA_REGISTRY_URL
              value: "https://psrc-v6px5.westeurope.azure.confluent.cloud"
            - name: SCHEMA_REGISTRY_API_KEY
              valueFrom:
                secretKeyRef:
                  name: ude-str-flink-gdc-job-schema-registry-secret
                  key: confluent_schema_registry_key
            - name: SCHEMA_REGISTRY_API_SECRET
              valueFrom:
                secretKeyRef:
                  name: ude-str-flink-gdc-job-schema-registry-secret
                  key: confluent_schema_registry_secret
            - name: KAFKA_CLUSTER_API_SECRET
              valueFrom:
                secretKeyRef:
                  name: ude-str-flink-gdc-job-kafka-api-secret
                  key: confluent_kafka_api_secret
            - name: KAFKA_CLUSTER_API_KEY
              valueFrom:
                secretKeyRef:
                  name: ude-str-flink-gdc-job-kafka-api-secret
                  key: confluent_kafka_api_key
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: ude-str-flink-gdc-job-db-password
                  key: postgresql_admin_password
            - name: AZURE_STORAGE_KEY
              valueFrom:
                secretKeyRef:
                  name: ude-str-flink-storage-access-key-secret
                  key: flink-storage-access-key
            - name: AZURE_CREDENTIAL_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: ude-str-flink-store-and-explore-spn-secret
                  key: azure_credential_client_secret
            - name: KAFKA_GDC_SOURCE_TOPIC_NAME
              value: "private.ude-str.data-ingestion.input.json"
            - name: KAFKA_VMS_SOURCE_TOPIC_NAME
              value: "private.ude-str.vms-connector.flattened.json"
            - name: KAFKA_VDC_SOURCE_TOPIC_NAME
              value: "private.ude-str.vdc-normalization-rules.json"
            - name: KAFKA_SINK_TOPIC_NAME
              value: "public.ude-str.gdc-output.json"
            - name: KAFKA_NON_RETRYABLE_DLQ_TOPIC_NAME
              value: "private.ude-str.non-retryable-dlq.json"
            - name: KAFKA_RETRYABLE_DLQ_TOPIC_NAME
              value: "private.ude-str.retryable-dlq.json"
            - name: FLINK_GROUP_ID
              value: "flink-job"
            - name: DB_URL
              value: "*****************************************************************************************"
            - name: DB_USER_NAME
              value: "psqladmin"
            - name: DB_CLAIMS_DATABASE_NAME
              value: "management-api"
            - name: DB_PROCESSING_CLAIM_TABLE_NAME
              value: "processing_claim"
            - name: DB_VDC_DATABASE_NAME
              value: "vdc"
            - name: DB_VDC_RULES_TABLE_NAME
              value: "normalization_rule"
            - name: API_USE_CASE_ID
              value: "data-ingestion"
            - name: ENRICHMENT_CONFIG
              value: "[{\"schemaId\":\"986a96e6-4c30-440c-9dcd-78e9d9451af9\",\"enrichmentData\":[{\"source\":\"private.ude-str.vms-connector.flattened.json\",\"type\":\"kafka\",\"fields\":[{\"sourceName\":\"vin\",\"outputName\":\"vin\"}]}]}]"
            - name: FCT_RULES
              value: "[{\"gid\": 100042,\"name\": \"instrumentClusterTime\",\"unit\": {\"rational\": {\"numerators\": {},\"denominator\": {}},\"identical\": true},\"value\": {\"linear\": {\"scale\": 2,\"offset\": 5},\"identical\": false}},{\"gid\": 1001,\"name\": \"Diag.ReadUncachedDtc\",\"unit\": {\"rational\": {\"numerators\": {},\"denominator\": {}},\"identical\": false},\"value\": {\"linear\": {},\"identical\": true}},{\"gid\": 1002,\"name\": \"Diag.ReadCachedDtcCL15Off\",\"unit\": {\"rational\": {\"numerators\": {},\"denominator\": {}},\"identical\": false},\"value\": {\"linear\": {},\"identical\": true}},{\"gid\": 1003,\"name\": \"Diag.BCallReadUncachedDtc\",\"unit\": {\"rational\": {\"numerators\": {},\"denominator\": {}},\"identical\": false},\"value\": {\"linear\": {},\"identical\": true}},{\"gid\": 1004,\"name\": \"Diag.Assembly\",\"unit\": {\"rational\": {\"numerators\": {},\"denominator\": {}},\"identical\": false},\"value\": {\"linear\": {},\"identical\": true}},{\"gid\": 1005,\"name\": \"Diag.ReadDtc\",\"unit\": {\"rational\": {\"numerators\": {},\"denominator\": {}},\"identical\": false},\"value\": {\"linear\": {},\"identical\": true}},{\"gid\": 1008,\"name\": \"MonitorAllVehicle_DTC\",\"unit\": {\"rational\": {\"numerators\": {},\"denominator\": {}},\"identical\": false},\"value\": {\"linear\": {},\"identical\": true}}]"
            - name: HADOOP_OPTIONAL_TOOLS
              value: hadoop-azure
            - name: AZURE_BLOB_ENDPOINT
              value: "https://studestoreexpmeuwdstrlm.blob.core.windows.net"
            - name: AZURE_CREDENTIAL_TENANT_ID
              value: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
            - name: AZURE_CREDENTIAL_CLIENT_ID
              value: "960273be-cc08-4eb3-8fb3-61213b7e912a"
          volumeMounts:
            - mountPath: /opt/flink/log
              name: flink-logs
            - mountPath: /opt/flink/downloads
              name: downloads
      volumes:
        - name: flink-logs
          emptyDir: { }
        - name: downloads
          emptyDir: { }
  flinkConfiguration:
    kubernetes.jobmanager.annotations: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/
    kubernetes.taskmanager.annotations: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/

    classloader.resolve-order: parent-first

    taskmanager.numberOfTaskSlots: "5"


    #jobmanager.heap.size: ""
    jobmanager.memory.process.size: "3GB"
    #taskmanager.heap.size: ""

    #taskmanager.memory.task.heap.size: "1GB"
    taskmanager.memory.process.size: "5GB"
    taskmanager.memory.managed.fraction: "0.1"
    env.java.opts.all: "--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED"
    job.autoscaler.enabled: "true"
    job.autoscaler.memory.tuning.enabled: "true"
    # Enable Adaptive scheduler to play the in-place rescaling.
    jobmanager.scheduler: adaptive
    #job.autoscaler.memory.tuning.overhead: "1"
    # jobmanager.memory.jvm-metaspace.size: "1GB"
    # Enable autoscale and scaling
    job.autoscaler.scaling.enabled: "true"
    job.autoscaler.stabilization.interval : 1m
    job.autoscaler.metrics.window : 1m
    # Enable flame graph for statistics !!!! Only for dev and pre-prod envs !!!!
    rest.flamegraph.enabled: "true"
    rest.flamegraph.refresh-interval: "10 m"
    rest.profiling.enabled: "true"

    metrics.reporter.prom.factory.class: org.apache.flink.metrics.prometheus.PrometheusReporterFactory
    metrics.reporter.prom.port: "9249"
    kubernetes.container.image.pull-secrets: mega-container-registry-secret

    # Flink State Backend, Storage account access key
    fs.azure.account.auth.type.udestrdeveuflinkstorage.blob.core.windows.net: SharedKey
    fs.azure.account.keyprovider.udestrdeveuflinkstorage.blob.core.windows.net: org.apache.flink.fs.azurefs.EnvironmentVariableKeyProvider
    fs.azure.account.key.udestrdeveuflinkstorage.dfs.core.windows.net: $AZURE_STORAGE_KEY

    # Checkpointing
    execution.checkpointing.dir: wasbs://<EMAIL>/flinkGdcJob/checkpoints
    state.checkpoints.dir: wasbs://<EMAIL>/flinkGdcJob/checkpoints
    execution.checkpointing.interval: "4 min"
    execution.checkpointing.mode: "EXACTLY_ONCE"
    execution.checkpointing.timeout: "10 min"
    execution.checkpointing.min-pause: "2 min"
    execution.checkpointing.max-concurrent-checkpoints: "1"

    execution.checkpointing.storage: filesystem
    # Increase incremental checkpointing to avoid huge size checkpointing
    execution.checkpointing.incremental: "true"
    
    # # RocksDB-Specific Tuning
    state.backend.type: rocksdb
    fs.azure.data.blocks.buffer: array
    
  ingress:
    template: "str-dev.apps.mega.cariad.cloud/{{name}}(/|$)(.*)"
    className: "nginx"
    annotations:
      nginx.ingress.kubernetes.io/auth-type: basic
      nginx.ingress.kubernetes.io/auth-secret: ude-str-flink-basic-auth
      nginx.ingress.kubernetes.io/auth-realm: "Authentication Required - Flink GDC Job"
      nginx.ingress.kubernetes.io/rewrite-target: "/$2"
  jobManager:
    resource:
      memory: "6Gi"
      cpu: 2
  taskManager:
    resource:
      memory: "6Gi"
      cpu: 2
  serviceAccount: riga-9ab4ff3b-identity
  job:
    jarURI: local:///opt/flink/lib/flink-job-all.jar
    entryClass: org.ude.GdcTransformationJob
    parallelism: 1
    upgradeMode: stateless
  logConfiguration:
    "logback-console.xml": |
      <configuration>
        <property name="pattern" value="%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread) %cyan(%-12.36logger{36}) %msg%n" />
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.out</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>ERROR</level>
                <onMatch>DENY</onMatch>
            </filter>h
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>WARN</level>
                <onMatch>DENY</onMatch>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <appender name="STDERR" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.err</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>WARN</level>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <root level="INFO">
            <appender-ref ref="STDOUT" />
            <appender-ref ref="STDERR" />
        </root>

        <!-- Specific loggers -->
        <logger name="akka" level="INFO"/>
        <logger name="org.apache.kafka" level="INFO"/>
        <logger name="org.apache.hadoop" level="INFO"/>
        <logger name="org.apache.zookeeper" level="INFO"/>
        <logger name="org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline" level="OFF"/>
      </configuration>
