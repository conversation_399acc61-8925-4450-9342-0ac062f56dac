apiVersion: v1
kind: Pod
metadata:
  name: azure-cli-pod
  namespace: str-dev
  labels:
    app: azure-cli
spec:
  containers:
  - name: azure-cli
    image: mcr.microsoft.com/azure-cli:latest
    command: ["/bin/bash", "-c"]
    args: ["while true; do sleep 30; done;"]
    env:
    - name: AZURE_CONFIG_DIR
      value: /home/<USER>/.azure
    resources:
      limits:
        cpu: 200m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
          - ALL
      runAsNonRoot: true
      runAsUser: 1000
    volumeMounts:
    - name: azure-config
      mountPath: /home/<USER>/.azure
  volumes:
  - name: azure-config
    emptyDir: {}
  restartPolicy: Always