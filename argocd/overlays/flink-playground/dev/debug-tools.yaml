apiVersion: apps/v1
kind: Deployment
metadata:
  name: debug-tools
  labels:
    app.kubernetes.io/name: debug-tools
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: debug-tools
  template:
    metadata:
      labels:
        app.kubernetes.io/name: debug-tools    
    spec:
      securityContext:
        runAsUser: 10001
        runAsGroup: 10001
        fsGroup: 10001
      containers:
      - name: debug-tools
        image: crmegahubwesteurope.azurecr.io/str-dev/debug-tools:0.1.0
        imagePullPolicy: Always
        env:
        - name: HOME
          value: /home/<USER>
        - name: AZURE_CONFIG_DIR
          value: /home/<USER>/.azure
        - name: TMPDIR
          value: /tmp
        livenessProbe:
          exec:
            command:
            - /bin/bash
            - -c
            - "exit 0"
          initialDelaySeconds: 5
          periodSeconds: 30
        readinessProbe:
          exec:
            command:
            - /bin/bash
            - -c
            - "exit 0"
          initialDelaySeconds: 5
          periodSeconds: 30
        command: ["/bin/bash"]
        args: ["-c", "while true; do sleep 30; done"]  
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          privileged: false
          readOnlyRootFilesystem: true
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        volumeMounts:
        - name: azure-config
          mountPath: /home/<USER>/.azure
        - name: kube-config
          mountPath: /home/<USER>/.kube
        - name: tmp
          mountPath: /tmp
        - name: var-tmp
          mountPath: /var/tmp
        - name: home-tmp
          mountPath: /home/<USER>/tmp
      volumes:
      - name: azure-config
        emptyDir: {}
      - name: kube-config
        emptyDir: {}
      - name: tmp
        emptyDir: {}
      - name: var-tmp
        emptyDir: {}
      - name: home-tmp
        emptyDir: {}
      restartPolicy: Always