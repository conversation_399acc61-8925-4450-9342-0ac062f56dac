apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: str-dev

# Patches the base/existing manifests
patches:
- path: roles.yaml

# Applies new/overlay specific manifests
resources:
- ../../../base/flink-playground
- pdb.yaml
- flink-external-secret.yaml
- flink-gdc-job-schema-registry-secret.yaml
- flink-gdc-job-kafka-api-secret.yaml
- flink-gdc-job-postgresql-secret.yaml
- flink-gdc-job-azure-blob-secret.yaml
- flink-basic-auth-external-secret.yaml
- flinkdeployment-gdc-pipeline-application-mode.yaml
- flinkdeployment-passthrough-application-mode.yaml
- flinkdeployment-vms-application-mode.yaml
- flink-storage-external-secret.yaml
- team_secretstore.yaml
- debug-tools.yaml