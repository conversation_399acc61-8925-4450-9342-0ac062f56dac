apiVersion: v1
kind: Pod
metadata:
  name: confluent-cli
  namespace: str-dev
spec:
  nodeSelector:
    megatron.cariad.technology/node-context: product
    megatron.cariad.technology/node-type: firewall-bypass
  tolerations:
  - key: "firewall"
    operator: "Equal"
    value: "bypass"
    effect: "NoSchedule"
  containers:
  - image: confluentinc/confluent-cli
    imagePullPolicy: Always
    name: confluent-cli
    resources:
      limits:
        cpu: 100m
        memory: 125Mi
      requests:
        cpu: 100m
        memory: 125Mi
    securityContext:
      allowPrivilegeEscalation: false
      privileged: false
    terminationMessagePath: /dev/termination-log
    terminationMessagePolicy: File
    tty: true