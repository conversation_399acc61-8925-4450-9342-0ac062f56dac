apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: ude-str-flink-gdc-job-external-secret-schema-registry-external-secret
  namespace: str-int 
spec:
  refreshInterval: 1h # Adjust this to your needs
  secretStoreRef:
    kind: SecretStore
    name: ude-str-flink-secret-store # Here you refer to the SecretStore

  target:
    name: ude-str-flink-gdc-job-schema-registry-secret # Here you define how the Kubernetes Secret should be named
    creationPolicy: Owner

  data:
  - secretKey: confluent_schema_registry_secret # <PERSON><PERSON><PERSON> defines the Key in the Kubernetes Secret 
    remoteRef:
      key: ude-str-int-eu-gdc-job-schema-registry-service-account-secret # RemoteRef Key defines how the Secret is named in the Azure Keyvault
  - secretKey: confluent_schema_registry_key # <PERSON><PERSON><PERSON> defines the Key in the Kubernetes Secret 
    remoteRef:
      key: ude-str-int-eu-gdc-job-schema-registry-service-account-id # RemoteRef Key defines how the Secret is named in the Azure Keyvault