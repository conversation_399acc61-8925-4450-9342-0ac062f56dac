apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: flink-gdc-application-mode-w-checkpoint
spec:
  restartNonce: 6
  image: crmegahubwesteurope.azurecr.io/str-int/gdc-job:1.0-SNAPSHOT
  flinkVersion: v1_20
  mode: native
  podTemplate:
    metadata:
      labels:
        app.kubernetes.io/name: "flink-gdc-application-mode-w-checkpoint"
    spec:
      containers:
        - name: flink-main-container
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
            runAsNonRoot: true
            runAsUser: 9999
          env:
            - name: env.java.opts.all
              value: "--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED"
            - name: BOOTSTRAP_SERVERS
              value: "pkc-vr3yjz.westeurope.azure.confluent.cloud:9092"
            - name: SCHEMA_REGISTRY_URL
              value: "https://psrc-j39np.westeurope.azure.confluent.cloud"
            - name: SCHEMA_REGISTRY_API_KEY
              valueFrom:
                secretKeyRef:
                  name: ude-str-flink-gdc-job-schema-registry-secret
                  key: confluent_schema_registry_key
            - name: SCHEMA_REGISTRY_API_SECRET
              valueFrom:
                secretKeyRef:
                  name: ude-str-flink-gdc-job-schema-registry-secret
                  key: confluent_schema_registry_secret
            - name: KAFKA_CLUSTER_API_SECRET
              valueFrom:
                secretKeyRef:
                  name: ude-str-flink-gdc-job-kafka-api-secret
                  key: confluent_kafka_api_secret
            - name: KAFKA_CLUSTER_API_KEY
              valueFrom:
                secretKeyRef:
                  name: ude-str-flink-gdc-job-kafka-api-secret
                  key: confluent_kafka_api_key
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: ude-str-flink-gdc-job-db-password
                  key: postgresql_admin_password
            - name: AZURE_CREDENTIAL_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: ude-str-flink-store-and-explore-spn-secret
                  key: azure_credential_client_secret
            - name: AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: deployment-service-store-and-explore-upload-spn-secret
                  key: azure_credential_client_secret
            - name: AZURE_DOWNLOAD_CREDENTIAL_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: deployment-service-store-and-explore-download-spn-secret
                  key: azure_credential_client_secret
            - name: KAFKA_GDC_SOURCE_TOPIC_NAME
              value: "private.ude-str.16fb55f1-795b-4532-b5c3-5856d7f2c55d.input.json"
            - name: KAFKA_VMS_SOURCE_TOPIC_NAME
              value: "private.ude-str.vms-connector.flattened.json"
            - name: KAFKA_VDC_SOURCE_TOPIC_NAME
              value: "private.ude-str.vdc-normalization-rules.json"
            - name: KAFKA_SINK_TOPIC_NAME
              value: "public.ude-str.16fb55f1-795b-4532-b5c3-5856d7f2c55d.output.json"
            - name: KAFKA_NON_RETRYABLE_DLQ_TOPIC_NAME
              value: "private.ude-str.16fb55f1-795b-4532-b5c3-5856d7f2c55d.non-retryable.dlq.json"
            - name: KAFKA_RETRYABLE_DLQ_TOPIC_NAME
              value: "private.ude-str.16fb55f1-795b-4532-b5c3-5856d7f2c55d.non-retryable.dlq.json"
            - name: FLINK_GROUP_ID
              value: "flink-job-871d5790-ac96-4b75-ae7c-58c8ae0ff365"
            - name: DB_URL
              value: "******************************************************************************************"
            - name: DB_USER_NAME
              value: "psqladmin"
            - name: DB_CLAIMS_DATABASE_NAME
              value: "management-api"
            - name: DB_PROCESSING_CLAIM_TABLE_NAME
              value: "processing_claim"
            - name: DB_VDC_DATABASE_NAME
              value: "vdc"
            - name: DB_VDC_RULES_TABLE_NAME
              value: "normalization_rule"
            - name: API_USE_CASE_ID
              value: "16fb55f1-795b-4532-b5c3-5856d7f2c55d"
            - name: ENRICHMENT_CONFIG
              value: "[{\"schemaId\":\"67c2076d-2f8f-49dc-ae29-ed6b770f0842\",\"enrichmentData\":[{\"source\":\"private.ude-str.vms-connector.flattened.json\",\"type\":\"kafka\",\"fields\":[{\"sourceName\":\"vin\",\"outputName\":\"vin\"}]}]}]"
            - name: FCT_RULES
              value: '[{"gid": 100042,"name": "instrumentClusterTime","unit": {"new_unit": "second","rational": {"numerators": {},"denominator": {}},"identical": true},"value": {"linear": {"scale": 2,"offset": 5},"identical": false}},{"gid": 1001,"name": "Diag.ReadUncachedDtc","unit": {"rational": {"numerators": {},"denominator": {}},"identical": false},"value": {"linear": {},"identical": true}},{"gid": 1002,"name": "Diag.ReadCachedDtcCL15Off","unit": {"rational": {"numerators": {},"denominator": {}},"identical": false},"value": {"linear": {},"identical": true}},{"gid": 1003,"name": "Diag.BCallReadUncachedDtc","unit": {"rational": {"numerators": {},"denominator": {}},"identical": false},"value": {"linear": {},"identical": true}},{"gid": 1004,"name": "Diag.Assembly","unit": {"rational": {"numerators": {},"denominator": {}},"identical": false},"value": {"linear": {},"identical": true}},{"gid": 1005,"name": "Diag.ReadDtc","unit": {"rational": {"numerators": {},"denominator": {}},"identical": false},"value": {"linear": {},"identical": true}},{"gid": 1008,"name": "MonitorAllVehicle_DTC","unit": {"rational": {"numerators": {},"denominator": {}},"identical": false},"value": {"linear": {},"identical": true}},{"gid": 1009,"name": "MonitorAllVehicle_BTC","unit": {"rational": {"numerators": {},"denominator": {}},"none": true},"value": {"linear": {},"identical": true}},{"gid": 1010,"name": "VelocityAllVehicle_BTC","unit": {"new_unit": "km/hr","rational": {"numerators": {},"denominator": {}},"none": false},"value": {"linear": {},"identical": true}}]'
            - name: HADOOP_OPTIONAL_TOOLS
              value: hadoop-azure
            - name: AZURE_UPLOAD_BLOB_ENDPOINT
              value: "https://studestoreexpmeuwistrlm.blob.core.windows.net"
            - name: AZURE_UPLOAD_CREDENTIAL_CLIENT_ID
              value: "39f7f81d-e6e9-4a20-9827-a333d98fdc66"
            - name: AZURE_UPLOAD_CREDENTIAL_TENANT_ID
              value: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
            - name: AZURE_DOWNLOAD_BLOB_ENDPOINT
              value: "https://studestoreexpmeuwistra2.blob.core.windows.net"
            - name: AZURE_DOWNLOAD_CREDENTIAL_CLIENT_ID
              value: "39f7f81d-e6e9-4a20-9827-a333d98fdc66"
            - name: AZURE_DOWNLOAD_CREDENTIAL_TENANT_ID
              value: "c640cbdb-21b6-4d3e-851e-7330aee92bbf"
            - name: AZURE_CREDENTIAL_TENANT_ID
              value: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
          volumeMounts:
            - mountPath: /opt/flink/log
              name: flink-logs
            - mountPath: /opt/flink/downloads
              name: downloads
      volumes:
        - name: flink-logs
          emptyDir: { }
        - name: downloads
          emptyDir: { }

  flinkConfiguration:
    kubernetes.jobmanager.annotations: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/
    kubernetes.taskmanager.annotations: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/
    classloader.resolve-order: parent-first
    taskmanager.numberOfTaskSlots: "3"
    #jobmanager.heap.size: ""
    jobmanager.memory.process.size: "3GB"
    # memory fraction for task manager
    #taskmanager.memory.fraction: 1.2
    #taskmanager.heap.size: ""

    #taskmanager.memory.task.heap.size: "1GB"
    taskmanager.memory.process.size: "8GB"
    taskmanager.memory.managed.fraction: "0.1"
    # taskmanager.memory.network.fraction: "0.4"
    # taskmanager.memory.network.max: "1.5GB"



    env.java.opts.all: "--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED"
    job.autoscaler.enabled: "true"
    job.autoscaler.memory.tuning.enabled: "false"
    # Enable Adaptvie scheduler to play the in-place rescaling.
    jobmanager.scheduler: adaptive
    job.autoscaler.memory.tuning.overhead: "0.5"
    # Enable autoscale and scaling
    job.autoscaler.scaling.enabled: "true"
    job.autoscaler.stabilization.interval: 1m
    job.autoscaler.target.utilization: "0.6"
    job.autoscaler.target.utilization.boundary: "0.2"
    job.autoscaler.metrics.window: 5m
    job.autoscaler.restart.time: 2m
    # Enable flame graph for statistics !!!! Only for dev and pre-prod envs !!!!
    rest.flamegraph.enabled: "true"
    rest.flamegraph.refresh-interval: "10m"
    rest.profiling.enabled: "true"

    metrics.reporter.prom.factory.class: org.apache.flink.metrics.prometheus.PrometheusReporterFactory
    metrics.reporter.prom.port: "9249"
    kubernetes.container.image.pull-secrets: mega-container-registry-secret
    kubernetes.container.image.pull-policy: Always

    # # Checkpointing
    # execution.checkpointing.dir: abfss://<EMAIL>/intflinkGdcJob/checkpoints
    # execution.checkpointing.interval: "4 min"
    # execution.checkpointing.mode: "EXACTLY_ONCE"
    # execution.checkpointing.timeout: "10 min"
    # execution.checkpointing.min-pause: "2 min"
    # execution.checkpointing.max-concurrent-checkpoints: "1"
    
    # fs.azure.abfs.endpoint: "udestrinteuflinkstorage.dfs.core.windows.net"
    # fs.azure.account.auth.type: OAuth
    # fs.azure.account.oauth.provider.type: org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider
    # fs.azure.account.oauth2.client.id: "253d76fe-1405-4cf7-ae71-e7f65666142f"
    # fs.azure.account.oauth2.client.secret: "****************************************"
    # fs.azure.account.oauth2.client.endpoint: https://login.microsoftonline.com/${env.AZURE_CREDENTIAL_TENANT_ID}/oauth2/token

    # execution.checkpointing.dir: abfss://ude-str-int-eu-flink-storage@$udestrinteuflinkstorage.dfs.core.windows.net/intflinkGdcJob/checkpoints
    # execution.checkpointing.externalized-checkpoint-retention: RETAIN_ON_CANCELLATION
    # execution.checkpointing.savepoint-dir: abfss://flink@$udestrinteuflinkstorage.dfs.core.windows.net/intflinkGdcJob/savepoints

    # fs.azure.abfs.endpoint: udestrinteuflinkstorage.dfs.core.windows.net
    # fs.azure.account.auth.type: OAuth
    # fs.azure.account.oauth2.client.endpoint: https://login.microsoftonline.com/c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c/oauth2/token
    # fs.azure.account.oauth2.client.id: "253d76fe-1405-4cf7-ae71-e7f65666142f"
    # fs.azure.account.oauth2.client.secret: "****************************************"
    # fs.azure.account.oauth.provider.type: org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider
    # fs.azure.data.blocks.buffer: bytebuffer
    # fs.defaultFS: abfss://ude-str-int-eu-flink-storage@$udestrinteuflinkstorage.dfs.core.windows.net

        # Checkpointing
    execution.checkpointing.dir: abfss://<EMAIL>/intflinkGdcJob/checkpoints
    execution.checkpointing.interval: "15 min"
    execution.checkpointing.mode: "EXACTLY_ONCE"
    execution.checkpointing.timeout: "15 min"
    execution.checkpointing.min-pause: "2 min"
    execution.checkpointing.max-concurrent-checkpoints: "1"
    execution.checkpointing.incremental: "true"
    execution.checkpointing.num-retained: "2"
    
    fs.azure.abfs.endpoint: "udestrinteuflinkstorage.dfs.core.windows.net"
    fs.azure.account.auth.type: OAuth
    fs.azure.account.oauth.provider.type: org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider
    fs.azure.account.oauth2.client.id: "253d76fe-1405-4cf7-ae71-e7f65666142f"
    fs.azure.account.oauth2.client.secret: "****************************************"
    fs.azure.account.oauth2.client.endpoint: https://login.microsoftonline.com/${env.AZURE_CREDENTIAL_TENANT_ID}/oauth2/token


    # #Flink State Backend, Storage account access key
    # fs.azure.account.auth.type.udestrdeveuflinkstorage.blob.core.windows.net: SharedKey
    # # fs.azure.account.keyprovider.udestrdeveuflinkstorage.blob.core.windows.net: org.apache.flink.fs.azurefs.EnvironmentVariableKeyProvider
    # fs.azure.account.key.udestrdeveuflinkstorage.dfs.core.windows.net: eTKUeXYc8R7v4q/IzaKhcnRO2BR3+oaj5AQ3M79YO9Xia4QwYfwdeMvIjvNChW65m2d9q0bPhdXY+AStOV1abw==

    
    # #Checkpointing
    # execution.checkpointing.dir: wasbs://<EMAIL>/flinkGdcJob/checkpoints
    # execution.checkpointing.interval: "4 min"
    # execution.checkpointing.mode: "EXACTLY_ONCE"
    # execution.checkpointing.timeout: "10 min"
    # execution.checkpointing.min-pause: "2 min"
    # execution.checkpointing.max-concurrent-checkpoints: "1"

    # # RocksDB-Specific Tuning
    state.backend.type: rocksdb
    fs.azure.data.blocks.buffer: array

  ingress:
    template: "str-int.apps.mega.cariad.cloud/{{name}}(/|$)(.*)"
    className: "nginx"
    annotations:
      nginx.ingress.kubernetes.io/auth-type: basic
      nginx.ingress.kubernetes.io/auth-secret: ude-str-flink-basic-auth
      nginx.ingress.kubernetes.io/auth-realm: "Authentication Required - Flink Playground"
      nginx.ingress.kubernetes.io/rewrite-target: "/$2"
  jobManager:
    resource:
      memory: "6Gi"
      cpu: 2
  taskManager:
    resource:
      memory: "8Gi"
      cpu: 5
  serviceAccount: flink
  job:
    jarURI: local:///opt/flink/lib/flink-job-all.jar
    entryClass: org.ude.GdcTransformationJob
    parallelism: 1
    upgradeMode: stateless
  logConfiguration:
    "logback-console.xml": |
      <configuration>
        <property name="pattern" value="%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread) %cyan(%-12.36logger{36}) %msg%n" />
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.out</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>ERROR</level>
                <onMatch>DENY</onMatch>
            </filter>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>WARN</level>
                <onMatch>DENY</onMatch>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <appender name="STDERR" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.err</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>WARN</level>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <root level="INFO">
            <appender-ref ref="STDOUT" />
            <appender-ref ref="STDERR" />
        </root>

        <!-- Specific loggers -->
        <logger name="akka" level="WARN"/>
        <logger name="org.apache.kafka" level="WARN"/>
        <logger name="org.apache.hadoop" level="INFO"/>
        <logger name="org.apache.zookeeper" level="WARN"/>
        <logger name="org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline" level="OFF"/>
      </configuration>
