apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: flink-application-mode
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: "flink-application-mode"
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: flink-passthrough-application-mode
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: "flink-passthrough-application-mode"
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: flink-gdc-application-mode
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: "flink-gdc-application-mode"
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: flink-kafka-postgres
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: "flink-kafka-postgres"
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: flink-vms-application-mode
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: "flink-vms-application-mode"
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: gdc-read-replica-test
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: "gdc-read-replica-test"
