apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  labels:
    argocd.argoproj.io/instance: str-pre_deployment-service
  name: gdc-read-replica-test
  namespace: str-pre
spec:
  flinkConfiguration:
    _ude.pipeline.dataOrderId: dd9250cf-b1bb-4a31-b84f-9a3020bf7ac1
    _ude.pipeline.name: streaming-pipeline-dd9250cf-b1bb-4a31-b84f-9a3020bf7ac1
    _ude.pipeline.projectId: 90bc3081-7a42-4a40-9193-b5e2d73a80f6
    _ude.pipeline.revision: "1"
    kubernetes.container.image.pull-secrets: mega-container-registry-secret
    kubernetes.jobmanager.annotations: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/
    kubernetes.taskmanager.annotations: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/
    state.checkpoints.dir: UDE_REMOVED
    state.savepoints.dir: UDE_REMOVED
  flinkVersion: v1_20
  image: crmegahubwesteurope.azurecr.io/str-pre/gdc-job:main
  imagePullPolicy: Always
  ingress:
    annotations:
      nginx.ingress.kubernetes.io/auth-realm: Authentication Required - Flink Playground
      nginx.ingress.kubernetes.io/auth-secret: deployment-service-flink-basic-auth
      nginx.ingress.kubernetes.io/auth-type: basic
      nginx.ingress.kubernetes.io/rewrite-target: /$2
    className: nginx
    template: str-pre.apps.mega.cariad.cloud/{{name}}(/|$)(.*)
  job:
    args: []
    entryClass: org.ude.GdcTransformationJob
    jarURI: local:///opt/flink/lib/flink-job-all.jar
    parallelism: 1
    state: running
    upgradeMode: savepoint
  jobManager:
    replicas: 1
    resource:
      cpu: 1
      memory: 1Gi
  logConfiguration:
    logback-console.xml: |
      <configuration>
        <property name="pattern" value="%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread) %cyan(%-12.36logger{36}) %msg%n" />
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.out</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>ERROR</level>
                <onMatch>DENY</onMatch>
            </filter>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>WARN</level>
                <onMatch>DENY</onMatch>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <appender name="STDERR" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.err</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>WARN</level>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <root level="INFO">
            <appender-ref ref="STDOUT" />
            <appender-ref ref="STDERR" />
        </root>

        <!-- Specific loggers -->
        <logger name="akka" level="INFO"/>
        <logger name="org.apache.kafka" level="INFO"/>
        <logger name="org.apache.hadoop" level="INFO"/>
        <logger name="org.apache.zookeeper" level="INFO"/>
        <logger name="org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline" level="OFF"/>
      </configuration>
  mode: native
  podTemplate:
    metadata:
      labels:
        app.kubernetes.io/name: gdc-dd9250cf-b1bb-4a31-b84f-9a3020bf7ac1
        dataOrderId: dd9250cf-b1bb-4a31-b84f-9a3020bf7ac1
        jobType: gdc-job
        pipelineName: streaming-pipeline-dd9250cf-b1bb-4a31-b84f-9a3020bf7ac1
        projectId: 90bc3081-7a42-4a40-9193-b5e2d73a80f6
    spec:
      containers:
      - env:
        - name: SCHEMA_REGISTRY_API_KEY
          valueFrom:
            secretKeyRef:
              key: confluent_schema_registry_key
              name: deployment-service-gdc-job-schema-registry-secret
        - name: SCHEMA_REGISTRY_API_SECRET
          valueFrom:
            secretKeyRef:
              key: confluent_schema_registry_secret
              name: deployment-service-gdc-job-schema-registry-secret
        - name: KAFKA_CLUSTER_API_SECRET
          valueFrom:
            secretKeyRef:
              key: confluent_kafka_api_secret
              name: deployment-service-gdc-job-kafka-credentials
        - name: KAFKA_CLUSTER_API_KEY
          valueFrom:
            secretKeyRef:
              key: confluent_kafka_api_key
              name: deployment-service-gdc-job-kafka-credentials
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: postgresql_admin_password
              name: deployment-service-db-password
        - name: AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: azure_credential_client_secret
              name: deployment-service-store-and-explore-upload-spn-secret
        - name: AZURE_DOWNLOAD_CREDENTIAL_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: azure_credential_client_secret
              name: deployment-service-store-and-explore-download-spn-secret
        - name: FLINK_CONF_DIR
          value: /opt/flink/conf-ude
        - name: HADOOP_OPTIONAL_TOOLS
          value: hadoop-azure
        - name: BOOTSTRAP_SERVERS
          value: "pkc-1j8k53.westeurope.azure.confluent.cloud:9092"
        - name: SCHEMA_REGISTRY_URL
          value: "https://psrc-j39np.westeurope.azure.confluent.cloud"
        - name: KAFKA_GDC_SOURCE_TOPIC_NAME
          value: "private.ude-str.data-ingestion.input.json"
        - name: KAFKA_VMS_SOURCE_TOPIC_NAME
          value: "private.ude-str.vms-connector.flattened.json"
        - name: KAFKA_SINK_TOPIC_NAME
          value: "public.ude-str.gdc-output.json"
        - name: KAFKA_NON_RETRYABLE_DLQ_TOPIC_NAME
          value: "private.ude-str.non-retryable-dlq.json"
        - name: FLINK_GROUP_ID
          value: "flink-job-2"
        - name: DB_URL
          value: "***********************************************************************************************"
        - name: DB_USER_NAME
          value: "psqladmin"
        - name: DB_CLAIMS_DATABASE_NAME
          value: "management-api"
        - name: DB_PROCESSING_CLAIM_TABLE_NAME
          value: "processing_claim"
        - name: DB_VDC_DATABASE_NAME
          value: "signals"
        - name: DB_VDC_RULES_TABLE_NAME
          value: "normalization_rule"
        - name: API_USE_CASE_ID
          value: "data-ingestion"
        - name: DATA_ORDER_RETENTION_DAYS
          value: "1"
        - name: ENRICHMENT_CONFIG
          value: "[{\"schemaId\":\"67c2076d-2f8f-49dc-ae29-ed6b770f0842\",\"enrichmentData\":[{\"source\":\"private.ude-str.vms-connector.flattened.json\",\"type\":\"kafka\",\"fields\":[{\"sourceName\":\"vin\",\"outputName\":\"vin\"}]}]}]"
        - name: FCT_RULES
          value: '[{"gid": 100042,"name": "instrumentClusterTime","unit": {"new_unit": "second","rational": {"numerators": {},"denominator": {}},"identical": true},"value": {"linear": {"scale": 2,"offset": 5},"identical": false}},{"gid": 1001,"name": "Diag.ReadUncachedDtc","unit": {"rational": {"numerators": {},"denominator": {}},"identical": false},"value": {"linear": {},"identical": true}},{"gid": 1002,"name": "Diag.ReadCachedDtcCL15Off","unit": {"rational": {"numerators": {},"denominator": {}},"identical": false},"value": {"linear": {},"identical": true}},{"gid": 1003,"name": "Diag.BCallReadUncachedDtc","unit": {"rational": {"numerators": {},"denominator": {}},"identical": false},"value": {"linear": {},"identical": true}},{"gid": 1004,"name": "Diag.Assembly","unit": {"rational": {"numerators": {},"denominator": {}},"identical": false},"value": {"linear": {},"identical": true}},{"gid": 1005,"name": "Diag.ReadDtc","unit": {"rational": {"numerators": {},"denominator": {}},"identical": false},"value": {"linear": {},"identical": true}},{"gid": 1008,"name": "MonitorAllVehicle_DTC","unit": {"rational": {"numerators": {},"denominator": {}},"identical": false},"value": {"linear": {},"identical": true}},{"gid": 1009,"name": "MonitorAllVehicle_BTC","unit": {"rational": {"numerators": {},"denominator": {}},"none": true},"value": {"linear": {},"identical": true}},{"gid": 1010,"name": "VelocityAllVehicle_BTC","unit": {"new_unit": "km/hr","rational": {"numerators": {},"denominator": {}},"none": false},"value": {"linear": {},"identical": true}}]'
        envFrom:
        - configMapRef:
            name: deployment-service-store-and-explore-config
        - configMapRef:
            name: deployment-service-ude-flink-storage-config
        name: flink-main-container
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          runAsUser: 9999
        volumeMounts:
        - mountPath: /opt/flink/conf-ude
          name: ude-flink-conf-temp
        - mountPath: /opt/flink/log
          name: flink-logs
        - mountPath: /opt/flink/downloads
          name: downloads
      initContainers:
      - command:
        - /bin/sh
        - /opt/ude/scripts/init-flink.sh
        env:
        - name: FLINK_APPLICATION_NAME
          value: gdc-dd9250cf-b1bb-4a31-b84f-9a3020bf7ac1
        - name: AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: azure_credential_client_secret
              name: deployment-service-store-and-explore-upload-spn-secret
        - name: AZURE_UDE_CREDENTIAL_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: azure_credential_client_secret
              name: deployment-service-ude-flink-storage-spn-secret
              optional: true
        envFrom:
        - configMapRef:
            name: deployment-service-store-and-explore-config
        - configMapRef:
            name: deployment-service-ude-flink-storage-config
        image: busybox
        name: flink-init-container
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          runAsUser: 9999
        volumeMounts:
        - mountPath: /opt/ude/scripts
          name: ude-shell-scripts
        - mountPath: /tmp/ude/flink-custom-configs
          name: ude-flink-custom-configs
        - mountPath: /tmp/ude/flink-devops-tuning
          name: ude-flink-devops-tuning-configs
        - mountPath: /tmp/ude/flink-conf
          name: ude-flink-conf-temp
        - mountPath: /tmp/real-flink-config
          name: flink-config-volume
      volumes:
      - configMap:
          name: deployment-service-ude-shell-scripts
        name: ude-shell-scripts
      - configMap:
          name: deployment-service-flink-conf-yaml
        name: ude-flink-custom-configs
      - configMap:
          name: devops-tuning-gdc-read-replica-test
          optional: true
        name: ude-flink-devops-tuning-configs
      - emptyDir: {}
        name: ude-flink-conf-temp
      - emptyDir: {}
        name: flink-logs
      - emptyDir: {}
        name: downloads
  serviceAccount: deployment-service
  taskManager:
    resource:
      cpu: 6
      memory: 4Gi
