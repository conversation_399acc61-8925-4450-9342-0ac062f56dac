apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: ude-str-flink-basic-auth-external-secret
spec:
  refreshInterval: 1h # Adjust this to your needs
  secretStoreRef:
    kind: SecretStore
    name: ude-str-flink-secret-store # Here you refer to the SecretStore

  target:
    name: ude-str-flink-basic-auth # Here you define how the Kubernetes Secret should be named
    creationPolicy: Owner

  data:
  - secretKey: auth # <PERSON><PERSON><PERSON> defines the Key in the Kubernetes Secret 
    remoteRef:
      key: ude-str-prd-eu-basicauth # RemoteRef Key defines how the Secret is named in the Azure Keyvault
