apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: ude-str-flink-gdc-job-external-secret-kafka-api-external-secret
  namespace: str-prd
spec:
  refreshInterval: 1h # Adjust this to your needs
  secretStoreRef:
    kind: SecretStore
    name: ude-str-flink-secret-store # Here you refer to the SecretStore

  target:
    name: ude-str-flink-gdc-job-kafka-api-secret # Here you define how the Kubernetes Secret should be named
    creationPolicy: Owner

  data:
  - secretKey: confluent_kafka_api_secret # Secret<PERSON><PERSON> defines the Key in the Kubernetes Secret 
    remoteRef:
      key: ude-str-prd-eu-gdc-job-kafka-service-account-public-secret # RemoteRef Key defines how the Secret is named in the Azure Keyvault
  - secretKey: confluent_kafka_api_key # <PERSON><PERSON><PERSON> defines the Key in the Kubernetes Secret 
    remoteRef:
      key: ude-str-prd-eu-gdc-job-kafka-service-account-public-id # RemoteRef Key defines how the Secret is named in the Azure Keyvault
