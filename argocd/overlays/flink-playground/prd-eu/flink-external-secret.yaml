apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: ude-str-flink-external-secret
spec:
  refreshInterval: 1h # Adjust this to your needs
  secretStoreRef:
    kind: SecretStore
    name: ude-str-flink-secret-store # Here you refer to the SecretStore

  target:
    name: ude-str-flink-http-auth-secret # Here you define how the Kubernetes Secret should be named
    creationPolicy: Owner

  data:
  - secretKey: github_token # <PERSON><PERSON><PERSON> defines the Key in the Kubernetes Secret 
    remoteRef:
      key: ude-str-flink-httpauth-secret # RemoteRef Key defines how the Secret is named in the Azure Keyvault