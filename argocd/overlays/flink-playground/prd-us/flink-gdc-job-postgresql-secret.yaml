apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: ude-str-flink-gdc-job-postgresql-external-secret
spec:
  refreshInterval: 1h # Adjust this to your needs
  secretStoreRef:
    kind: SecretStore
    name: ude-str-flink-secret-store # Here you refer to the SecretStore

  target:
    name: ude-str-flink-gdc-job-db-password # Here you define how the Kubernetes Secret should be named
    creationPolicy: Owner

  data:
  - secretKey: postgresql_admin_password # Secret<PERSON><PERSON> defines the Key in the Kubernetes Secret 
    remoteRef:
      key: ude-str-prd-us-psql-admin-password # RemoteRef Key defines how the Secret is named in the Azure Keyvault