apiVersion: opentelemetry.io/v1alpha1
kind: OpenTelemetryCollector
metadata:
  name: otel-sidecar
spec:
  mode: sidecar
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 100m
      memory: 64Mi
  securityContext:
    allowPrivilegeEscalation: false
    capabilities:
      drop:
        - ALL
    runAsNonRoot: true
  config: |
    receivers:
      filelog:
        include: ["/var/log/generator.log"]
        start_at: beginning
      otlp:
        protocols:
          grpc:
          http:
    processors:
      batch:
      resourcedetection:
        detectors: [env]
        timeout: 2s
        override: false
    exporters:
      debug:
        verbosity: detailed
      otlp:
        endpoint: "http://otel-gateway-collector:4317"
        tls:
          insecure: true
    service:
      telemetry:
        logs:
          level: "debug"
      pipelines:
        traces:
          receivers: [otlp]
          processors: [resourcedetection]
          exporters: [otlp,debug]
        metrics:
          receivers: [otlp]
          processors: [resourcedetection]
          exporters: [otlp,debug]
