apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/component: exporter
    app.kubernetes.io/name: kube-state-metrics
    app.kubernetes.io/version: 2.12.0
  name: kube-state-metrics
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: kube-state-metrics
  template:
    metadata:
      labels:
        app.kubernetes.io/component: exporter
        app.kubernetes.io/name: kube-state-metrics
        app.kubernetes.io/version: 2.12.0
    spec:
      automountServiceAccountToken: true
      serviceAccountName: kube-state-metrics
      containers:
      - image: crmegahubeastus2.azurecr.io/docker.io/bitnami/kube-state-metrics:2.12.0
        args:
          - '--namespaces=udestrus-prd'
          - '--resources=deployments,endpoints,services,ingresses,jobs,daemonsets,cronjobs,configmaps,horizontalpodautoscalers,poddisruptionbudgets,pods,replicasets,resourcequotas,secrets,statefulsets'
        resources:
          requests:
            memory: "50Mi"
            cpu: "10m"
          limits:
            memory: "75Mi"
            cpu: "15m"
        livenessProbe:
          httpGet:
            path: /livez
            port: 8080
          initialDelaySeconds: 5
          timeoutSeconds: 5
        name: kube-state-metrics
        ports:
        - containerPort: 8080
          name: http-metrics
        - containerPort: 8081
          name: telemetry
        readinessProbe:
          httpGet:
            path: /metrics
            port: 8081
          initialDelaySeconds: 5
          timeoutSeconds: 5
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 65534
          seccompProfile:
            type: RuntimeDefault
      nodeSelector:
        kubernetes.io/os: linux
