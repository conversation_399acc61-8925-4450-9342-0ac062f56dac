apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: confluent-cloud-metrics-credentials
  namespace: str-pre
spec:
  refreshInterval: 1h  # Adjust this to your needs
  secretStoreRef:
    kind: SecretStore
    name: ude-str-team-kv  # Reference to your SecretStore
  target:
    name: confluent-cloud-metrics-credentials  # Name of the Kubernetes Secret
    creationPolicy: Owner
  data:
    - secretKey: api-key  # Key in the Kubernetes Secret
      remoteRef:
        key: ude-str-pre-eu-metrics-viewer-service-account-id  # Key in Azure Key Vault
    - secretKey: api-secret  # Key in the Kubernetes Secret
      remoteRef:
        key: ude-str-pre-eu-metrics-viewer-service-account-secret  # Key in Azure Key Vault