apiVersion: logging.banzaicloud.io/v1beta1
kind: Flow
metadata:
  name: java-logs
spec:
  filters:
    # - parser:
    #     parse:
    #       type: multiline
    #       format_firstline: /\d{4}-\d{1,2}-\d{1,2}/
    #       multiline:
    #         - /^(?<time>\d{4}-\d{1,2}-\d{1,2} \d{1,2}:\d{1,2}:\d{1,2},\d{3}) (?<level>[^\s]+) \[(?<thread>.*)\] (?<message>.*)/
    - record_transformer:
        remove_keys: $.kubernetes.docker_id, $.kubernetes.annotations, $.kubernetes.container_hash, $.kubernetes.pod_id

  localOutputRefs:
  - java-logs
  match:
    - exclude:
        labels:
          app: opentelemetry
    - select: {}