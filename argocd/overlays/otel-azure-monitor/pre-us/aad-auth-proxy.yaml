apiVersion: apps/v1
kind: Deployment
metadata:
    labels:
        app.kubernetes.io/name: azuremonitor-proxy
    name: azuremonitor-proxy
spec:
    replicas: 1
    selector:
        matchLabels:
            app.kubernetes.io/name: azuremonitor-proxy
    template:
        metadata:
            labels:
                app.kubernetes.io/name: azuremonitor-proxy
                azure.workload.identity/use: "true"
            name: azuremonitor-proxy
        spec:
            serviceAccountName: boston-ca3f5937-identity
            containers:
            - name: aad-auth-proxy
              image: mcr.microsoft.com/azuremonitor/auth-proxy/prod/aad-auth-proxy/images/aad-auth-proxy:0.1.0-main-04-10-2024-7067ac84
              imagePullPolicy: Always
              securityContext:
                allowPrivilegeEscalation: false
                capabilities:
                  drop:
                    - ALL
                runAsNonRoot: true
                runAsUser: 1000
              resources:
                requests:
                  memory: "25Mi"
                  cpu: "10m"
                limits:
                  memory: "35Mi"
                  cpu: "13m"                
              ports:
              - name: auth-port
                containerPort: 8081
              env:
              - name: AUDIENCE
                value: https://monitor.azure.com/.default
              - name: TARGET_HOST
                value: https://ude-str-pre-us-czgt.eastus2-1.metrics.ingest.monitor.azure.com
              - name: LISTENING_PORT
                value: "8081"
              livenessProbe:
                httpGet:
                  path: /health
                  port: auth-port
                initialDelaySeconds: 5
                timeoutSeconds: 5
              readinessProbe:
                httpGet:
                  path: /ready
                  port: auth-port
                initialDelaySeconds: 5
                timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
    name: azuremonitor-proxy
spec:
    ports:
        - port: 80
          targetPort: 8081
    selector:
        app.kubernetes.io/name: azuremonitor-proxy
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: azuremonitor-proxy
spec:
  #minAvailable: 1
  maxUnavailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: "azuremonitor-proxy"
