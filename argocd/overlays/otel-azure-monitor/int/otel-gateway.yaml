--- 
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: ude-str-team-kv-store
spec:
  provider:
    azurekv:
      authType: WorkloadIdentity
      vaultUrl: "https://ude-str-int-eu-kv.vault.azure.net/" # The Keyvault URI
      serviceAccountRef:
        name: riga-d7008c25-identity

--- 
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: ude-str-otel-external-secret
  namespace: str-int 
spec:
  refreshInterval: 1h # Adjust this to your needs
  secretStoreRef:
    kind: SecretStore
    name: ude-str-team-kv-store # Here you refer to the SecretStore

  target:
    name: ude-str-int-eu-auditlog-kafka-credentials # Here you define how the Kubernetes Secret should be named
    creationPolicy: Owner

  data:
  - secretKey: kafka_sasl_username # <PERSON><PERSON><PERSON> defines the Key in the Kubernetes Secret 
    remoteRef:
      key: ude-str-int-eu-auditlog-kafka-id # RemoteRef Key defines how the Secret is named in the Azure Keyvault
  - secretKey: kafka_sasl_password # <PERSON><PERSON><PERSON> defines the Key in the Kubernetes Secret 
    remoteRef:
      key: ude-str-int-eu-auditlog-kafka-secret # RemoteRef Key defines how the Secret is named in the Azure Keyvault
---        
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: otel-collector
  namespace: str-int
rules:
- apiGroups:
  - ""
  resources:
  - pods
  - endpoints
  verbs:
  - get
  - watch
  - list
- apiGroups:
  - apps
  resources:
  - replicasets
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: otel-collector
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: otel-collector
subjects:
- kind: ServiceAccount
  name: metrics-scraper
  namespace: str-int
---
apiVersion: opentelemetry.io/v1alpha1
kind: OpenTelemetryCollector
metadata:
  name: otel-gateway
  namespace: str-int
  labels:
    app: opentelemetry
    component: otel-collector
    azure.workload.identity/use: "true"
spec:
  mode: deployment
  serviceAccount: "metrics-scraper"
  resources:
    limits:
      cpu: 1
      memory: 2Gi
    requests:
      cpu: 800m
      memory: 1.8Gi
  securityContext:
    allowPrivilegeEscalation: false
    capabilities:
      drop:
        - ALL
    runAsNonRoot: true
  env:
    - name: OPEN_TELEMETRY_COLLECTOR_KAFKA_API_KEY
      valueFrom:
        secretKeyRef:
          name: ude-str-int-eu-auditlog-kafka-credentials
          key: kafka_sasl_username
    - name: OPEN_TELEMETRY_COLLECTOR_KAFKA_API_SECRET
      valueFrom:
        secretKeyRef:
          name: ude-str-int-eu-auditlog-kafka-credentials
          key: kafka_sasl_password
    - name: OPEN_TELEMETRY_COLLECTOR_CLOUD_API_KEY
      valueFrom:
        secretKeyRef:
          name: confluent-cloud-metrics-credentials
          key: api-key
    - name: OPEN_TELEMETRY_COLLECTOR_CLOUD_API_SECRET
      valueFrom:
        secretKeyRef:
          name: confluent-cloud-metrics-credentials
          key: api-secret
  config: |
    receivers:
      otlp:
        protocols:
          grpc:
          http:
      kafka:
        protocol_version: 2.0.0
        brokers: [pkc-81j1pr.us-west-2.aws.confluent.cloud:9092]
        topic: confluent-audit-log-events
        encoding: json
        auth:
          sasl:
            username: "${env:OPEN_TELEMETRY_COLLECTOR_KAFKA_API_KEY}"
            password: "${env:OPEN_TELEMETRY_COLLECTOR_KAFKA_API_SECRET}"
            mechanism: "PLAIN"
          tls:
            insecure: false
      fluentforward:
        endpoint: 0.0.0.0:8006
      prometheus:
        config:
          scrape_configs:
            - job_name: Confluent Cloud
              scrape_interval: 2m
              scrape_timeout: 1m
              honor_timestamps: true
              static_configs:
                - targets:
                  - api.telemetry.confluent.cloud
              scheme: https
              basic_auth:
                username: ${OPEN_TELEMETRY_COLLECTOR_CLOUD_API_KEY}
                password: ${OPEN_TELEMETRY_COLLECTOR_CLOUD_API_SECRET}
              metrics_path: /v2/metrics/cloud/export
              params:
                "resource.kafka.id":
                  - lkc-v9kmd5
            - job_name: 'kube-state-metrics'
              static_configs:
                - targets: ['kube-state-metrics.str-int:8080']
            - job_name: 'kubernetes-pods'
              kubernetes_sd_configs:
              - role: pod
                namespaces:
                  names:
                    - "str-int"
              relabel_configs:
              - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
                action: keep
                regex: true
              - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
                action: replace
                target_label: __metrics_path__
                regex: (.+)
              - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
                action: replace
                regex: ([^:]+)(?::\d+)?;(\d+)
                replacement: $$1:$$2
                target_label: __address__
              - action: labelmap
                regex: __meta_kubernetes_pod_label_(.+)
              - source_labels: [__meta_kubernetes_namespace]
                action: replace
                target_label: kubernetes_namespace
              - source_labels: [__meta_kubernetes_pod_name]
                action: replace
                target_label: kubernetes_pod_name
            - job_name: "kubernetes-cadvisor"
              scheme: https
              tls_config:
                ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
                insecure_skip_verify: true
              authorization:
                credentials_file: /var/run/secrets/kubernetes.io/serviceaccount/token

              kubernetes_sd_configs:
                - role: node
              metric_relabel_configs:
                - source_labels: [namespace]
                  regex: str-prd
                  action: keep
                - source_labels: [__name__]
                  regex: 'container_cpu_usage_seconds_total|container_memory_working_set_bytes|container_memory_usage_bytes|container_spec_memory_limit_bytes|container_spec_cpu_quota|container_spec_cpu_period|http_requests_total'
                  action: keep
              relabel_configs:
                - target_label: __address__
                  replacement: kubernetes.default.svc:443
                - source_labels: [__meta_kubernetes_node_name]
                  regex: (.+)
                  target_label: __metrics_path__
                  replacement: /api/v1/nodes/$1/proxy/metrics/cadvisor

                - action: labelmap
                  regex: __meta_kubernetes_node_label_(.+)

    processors:
      batch:
      k8sattributes:
        passthrough: true
        auth_type: 'serviceAccount'
        extract:
          metadata: # extracted from the pod
            - k8s.namespace.name
            - k8s.pod.name
            - k8s.pod.start_time
            - k8s.pod.uid
            - k8s.deployment.name
            - k8s.node.name

        pod_association: # How to associate the data to a pod (order matters)
          - sources: # First try to use the value of the resource attribute k8s.pod.ip
              - from: resource_attribute
                name: k8s.pod.ip
          - sources: # Then try to use the value of the resource attribute k8s.pod.uid
              - from: resource_attribute
                name: k8s.pod.uid
          - sources: # If neither of those work, use the request's connection to get the pod IP.
              - from: connection

      attributes/static:
        actions:
          - key: "cloud.region"
            value: "westeurope"
            action: insert
          - key: "vwg.platform.env"
            value: "dint"
            action: insert
          - key: "vwg.platform.name"
            value: "mega"
            action: insert
          - key: "vwg.platform.region"
            value: "eu"
            action: insert
          - key: "vwg.platform.solution"
            value: "ude"
            action: insert
    
      transform/add_log_timestamp:
        log_statements:
          - context: log
            statements:
              - set(attributes["log_timestamp"], Now())

    exporters:
      debug:
        verbosity: detailed
      prometheusremotewrite:
        endpoint: "http://azuremonitor-proxy/dataCollectionRules/dcr-eb94dd3e647e4ac7a54b6b5904b8f0d4/streams/Microsoft-PrometheusMetrics/api/v1/write?api-version=2023-04-24"
      azuremonitor:
        connection_string: "InstrumentationKey=fb803b78-a995-46b2-a46a-8b67d8cc7f5a;IngestionEndpoint=https://westeurope-5.in.applicationinsights.azure.com/;LiveEndpoint=https://westeurope.livediagnostics.monitor.azure.com/;ApplicationId=022e90c9-712a-4d84-97a7-b5b7aadcda6a"
    service:
      telemetry:
        logs:
          level: "debug"
      pipelines:
        traces:          
          processors: [k8sattributes, attributes/static, transform/add_log_timestamp]
          receivers: [otlp]
          exporters: [azuremonitor]
        metrics:
          receivers: [otlp,prometheus]
          processors: [batch, attributes/static, transform/add_log_timestamp]
          exporters: [prometheusremotewrite]
        logs:
          receivers: [fluentforward,kafka]
          processors: [batch, attributes/static, transform/add_log_timestamp]
          exporters: [azuremonitor]
