[2025-06-10 18:08:37] Starting script to update FlinkDeployment 'gdc-1b7a3957-3729-4383-8d5c-6cf16dcd647e' in namespace 'str-int'
[2025-06-10 18:08:37] Savepoint functionality enabled - job will be gracefully stopped with savepoint
[2025-06-10 18:08:37] Savepoint base path: abfss://<EMAIL>/gdc-1b7a3957-3729-4383-8d5c-6cf16dcd647e/savepoints
[2025-06-10 18:08:37] No REST API URL provided - will use port-forwarding
[2025-06-10 18:08:38] Checking if gdc-1b7a3957-3729-4383-8d5c-6cf16dcd647e needs STORAGE_SCHEMA_ID...
[2025-06-10 18:08:38] STORAGE_SCHEMA_ID is missing in gdc-1b7a3957-3729-4383-8d5c-6cf16dcd647e, extracting from ENRICHMENT_CONFIG...
[2025-06-10 18:08:39] Found schemaId: 7b3ad26d-f90a-4370-acc6-bc8bb1b1dfa7
[2025-06-10 18:11:29] Starting savepoint creation for gdc-1b7a3957-3729-4383-8d5c-6cf16dcd647e...
[2025-06-10 18:11:29] Savepoint directory: abfss://<EMAIL>/gdc-1b7a3957-3729-4383-8d5c-6cf16dcd647e/savepoints/gdc-1b7a3957-3729-4383-8d5c-6cf16dcd647e-savepoint-1749571889
[2025-06-10 18:11:29] Setting up port-forwarding for Flink JobManager REST API...
[2025-06-10 18:11:29] Port-forwarding started with PID: 42712
[2025-06-10 18:11:32] Fetching running job ID...
[2025-06-10 18:11:32] No RUNNING job found for deployment: gdc-1b7a3957-3729-4383-8d5c-6cf16dcd647e
[2025-06-10 18:11:32] Will proceed without savepoint
[2025-06-10 18:11:32] Port-forwarding stopped
[2025-06-10 18:11:32] Patch to be applied:
[2025-06-10 18:11:32] Applying patch to add STORAGE_SCHEMA_ID=7b3ad26d-f90a-4370-acc6-bc8bb1b1dfa7 and restart with nonce=1749571719
[2025-06-10 18:11:33] Successfully patched gdc-1b7a3957-3729-4383-8d5c-6cf16dcd647e with STORAGE_SCHEMA_ID and triggered restart
[2025-06-10 18:11:33] Operation completed
[2025-06-10 18:11:33] Log file: flinkdeployment-update-20250610-180837.log
