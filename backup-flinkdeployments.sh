#!/bin/bash
# Script to backup all FlinkDeployment YAML manifests from a specified Kubernetes namespace
# Creates a timestamped directory with individual YAML files for each deployment

# Default values
NAMESPACE=""
BACKUP_DIR="flinkdeployment-backups-$(date +%Y%m%d-%H%M%S)"
LOG_FILE="flinkdeployment-backup-$(date +%Y%m%d-%H%M%S).log"
DRY_RUN=false

# Function to display usage information
usage() {
  echo "Usage: $0 [OPTIONS]"
  echo "Options:"
  echo "  -n, --namespace NAMESPACE    Specify the Kubernetes namespace (required)"
  echo "  -o, --output-dir DIRECTORY   Specify custom backup directory (default: $BACKUP_DIR)"
  echo "  -d, --dry-run                Show what would be backed up without creating files"
  echo "  -h, --help                   Display this help message"
  exit 1
}

# Function for logging
log() {
  local message="$1"
  local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] $message"
  echo "[$timestamp] $message" >> "$LOG_FILE"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    -n|--namespace)
      NAMESPACE="$2"
      shift 2
      ;;
    -o|--output-dir)
      BACKUP_DIR="$2"
      shift 2
      ;;
    -d|--dry-run)
      DRY_RUN=true
      shift
      ;;
    -h|--help)
      usage
      ;;
    *)
      echo "Unknown option: $1"
      usage
      ;;
  esac
done

# Validate required parameters
if [ -z "$NAMESPACE" ]; then
  echo "Error: Namespace is required"
  usage
fi

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
  echo "Error: kubectl is not installed or not in PATH"
  exit 1
fi

log "Starting backup of FlinkDeployment manifests from namespace '$NAMESPACE'"
if [ "$DRY_RUN" = true ]; then
  log "Running in DRY-RUN mode - no files will be created"
fi
log "Backup directory: $BACKUP_DIR"

# Get all FlinkDeployments in the namespace
DEPLOYMENTS=$(kubectl get flinkdeployments -n "$NAMESPACE" -o jsonpath='{.items[*].metadata.name}' 2>/dev/null)
if [ $? -ne 0 ]; then
  log "Error: Failed to get FlinkDeployments in namespace $NAMESPACE"
  exit 1
fi

if [ -z "$DEPLOYMENTS" ]; then
  log "No FlinkDeployments found in namespace $NAMESPACE"
  exit 0
fi

# List all deployments and ask for confirmation
echo "Found the following FlinkDeployments in namespace $NAMESPACE:"
for DEPLOYMENT in $DEPLOYMENTS; do
  echo "- $DEPLOYMENT"
done

if [ "$DRY_RUN" = false ]; then
  read -p "Do you want to proceed with backing up these deployments? (y/n): " CONFIRM
  if [[ ! "$CONFIRM" =~ ^[Yy]$ ]]; then
    log "Operation cancelled by user"
    exit 0
  fi
  
  # Create backup directory
  log "Creating backup directory: $BACKUP_DIR"
  mkdir -p "$BACKUP_DIR"
  if [ $? -ne 0 ]; then
    log "Error: Failed to create backup directory: $BACKUP_DIR"
    exit 1
  fi
fi

SUCCESSFUL_BACKUPS=0
FAILED_BACKUPS=0

for DEPLOYMENT in $DEPLOYMENTS; do
  log "Backing up $DEPLOYMENT..."
  
  if [ "$DRY_RUN" = false ]; then
    # Export the FlinkDeployment manifest as YAML
    BACKUP_FILE="$BACKUP_DIR/$DEPLOYMENT.yaml"
    kubectl get flinkdeployment "$DEPLOYMENT" -n "$NAMESPACE" -o yaml > "$BACKUP_FILE" 2>/dev/null
    
    if [ $? -eq 0 ]; then
      log "Successfully backed up $DEPLOYMENT to $BACKUP_FILE"
      ((SUCCESSFUL_BACKUPS++))
    else
      log "Failed to backup $DEPLOYMENT"
      ((FAILED_BACKUPS++))
    fi
  else
    log "[DRY-RUN] Would backup $DEPLOYMENT to $BACKUP_DIR/$DEPLOYMENT.yaml"
    ((SUCCESSFUL_BACKUPS++))
  fi
done

# Summary
log "Backup operation completed"
log "Summary:"
log "- Total deployments found: $((SUCCESSFUL_BACKUPS + FAILED_BACKUPS))"
log "- Successfully backed up: $SUCCESSFUL_BACKUPS"
log "- Failed to backup: $FAILED_BACKUPS"

if [ "$DRY_RUN" = false ]; then
  log "Backup directory: $BACKUP_DIR"
  log "Log file: $LOG_FILE"
  
  # List the backup files
  if [ $SUCCESSFUL_BACKUPS -gt 0 ]; then
    log "Backup files:"
    for DEPLOYMENT in $DEPLOYMENTS; do
      BACKUP_FILE="$BACKUP_DIR/$DEPLOYMENT.yaml"
      if [ -f "$BACKUP_FILE" ]; then
        log "- $BACKUP_FILE ($(du -h "$BACKUP_FILE" | cut -f1))"
      fi
    done
  fi
else
  log "This was a dry run. No files were created."
fi

echo "Done!"