#!/bin/bash
# Enhanced script to add STORAGE_SCHEMA_ID to FlinkDeployments and restart them
# This version combines both operations into a single atomic patch

# Default values
NAMESPACE=""
DRY_RUN=false
LOG_FILE="storage-schema-update-$(date +%Y%m%d-%H%M%S).log"

# Function to display usage information
usage() {
  echo "Usage: $0 [OPTIONS]"
  echo "Options:"
  echo "  -n, --namespace NAMESPACE  Specify the Kubernetes namespace (required)"
  echo "  -d, --dry-run              Show what would be changed without applying"
  echo "  -h, --help                 Display this help message"
  exit 1
}

# Function for logging
log() {
  local message="$1"
  local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] $message"
  echo "[$timestamp] $message" >> "$LOG_FILE"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    -n|--namespace)
      NAMESPACE="$2"
      shift 2
      ;;
    -d|--dry-run)
      DRY_RUN=true
      shift
      ;;
    -h|--help)
      usage
      ;;
    *)
      echo "Unknown option: $1"
      usage
      ;;
  esac
done

# Validate required parameters
if [ -z "$NAMESPACE" ]; then
  echo "Error: Namespace is required"
  usage
fi

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
  echo "Error: kubectl is not installed or not in PATH"
  exit 1
fi

# Check if jq is available
if ! command -v jq &> /dev/null; then
  echo "Error: jq is not installed or not in PATH"
  exit 1
fi

log "Starting script to add STORAGE_SCHEMA_ID to FlinkDeployments in $NAMESPACE namespace"
if [ "$DRY_RUN" = true ]; then
  log "Running in DRY-RUN mode - no changes will be applied"
fi

# Get all FlinkDeployments in the namespace
DEPLOYMENTS=$(kubectl get flinkdeployments -n "$NAMESPACE" -o jsonpath='{.items[*].metadata.name}' 2>/dev/null)
if [ $? -ne 0 ]; then
  log "Error: Failed to get FlinkDeployments in namespace $NAMESPACE"
  exit 1
fi

if [ -z "$DEPLOYMENTS" ]; then
  log "No FlinkDeployments found in namespace $NAMESPACE"
  exit 0
fi

# List all deployments and ask for confirmation
echo "Found the following FlinkDeployments in namespace $NAMESPACE:"
for DEPLOYMENT in $DEPLOYMENTS; do
  echo "- $DEPLOYMENT"
done

if [ "$DRY_RUN" = false ]; then
  read -p "Do you want to proceed with checking and patching these deployments? (y/n): " CONFIRM
  if [[ ! "$CONFIRM" =~ ^[Yy]$ ]]; then
    log "Operation cancelled by user"
    exit 0
  fi
fi

SUCCESSFUL_DEPLOYMENTS=0
FAILED_DEPLOYMENTS=0
SKIPPED_DEPLOYMENTS=0

for DEPLOYMENT in $DEPLOYMENTS; do
  log "Checking $DEPLOYMENT..."
  
  # Check if STORAGE_SCHEMA_ID is missing
  HAS_STORAGE_SCHEMA_ID=$(kubectl get flinkdeployment "$DEPLOYMENT" -n "$NAMESPACE" -o json | 
    jq '.spec.podTemplate.spec.containers[].env | map(select(.name == "STORAGE_SCHEMA_ID")) | length')
  
  if [ "$HAS_STORAGE_SCHEMA_ID" -eq "0" ]; then
    log "STORAGE_SCHEMA_ID is missing in $DEPLOYMENT, extracting from ENRICHMENT_CONFIG..."
    
    # Extract schemaId from ENRICHMENT_CONFIG
    ENRICHMENT_CONFIG=$(kubectl get flinkdeployment "$DEPLOYMENT" -n "$NAMESPACE" -o json | 
      jq -r '.spec.podTemplate.spec.containers[].env[] | select(.name == "ENRICHMENT_CONFIG") | .value')
    
    if [ -n "$ENRICHMENT_CONFIG" ]; then
      # Extract the schemaId from the ENRICHMENT_CONFIG JSON
      SCHEMA_ID=$(echo "$ENRICHMENT_CONFIG" | jq -r '.[0].schemaId // empty')
      
      if [ -n "$SCHEMA_ID" ]; then
        log "Found schemaId: $SCHEMA_ID"
        
        # Get the container index that has the env vars
        CONTAINER_INDEX=$(kubectl get flinkdeployment "$DEPLOYMENT" -n "$NAMESPACE" -o json |  
          jq '.spec.podTemplate.spec.containers | map(.name == "flink-main-container") | index(true) // 0')
        
        # Generate a restartNonce value (timestamp-based)
        RESTART_NONCE=$(date +%s)
        
        # Create a combined JSON patch that adds the new env var and updates restartNonce
        PATCH=$(cat <<EOF
[
  {
    "op": "add",
    "path": "/spec/podTemplate/spec/containers/$CONTAINER_INDEX/env/-",
    "value": {
      "name": "STORAGE_SCHEMA_ID",
      "value": "$SCHEMA_ID"
    }
  },
  {
    "op": "replace",
    "path": "/spec/restartNonce",
    "value": $RESTART_NONCE
  }
]
EOF
)
        
        # Display the patch
        log "Combined patch to be applied (adds STORAGE_SCHEMA_ID and updates restartNonce to $RESTART_NONCE):"
        echo "$PATCH"
        
        # Ask for confirmation for each deployment if not in dry-run mode
        if [ "$DRY_RUN" = false ]; then
          read -p "Apply this patch to $DEPLOYMENT? (y/n): " CONFIRM_PATCH
          if [[ ! "$CONFIRM_PATCH" =~ ^[Yy]$ ]]; then
            log "Skipping $DEPLOYMENT by user request"
            ((SKIPPED_DEPLOYMENTS++))
            continue
          fi
          
          # Apply the combined JSON patch
          log "Applying combined patch to add STORAGE_SCHEMA_ID=$SCHEMA_ID and restart with nonce=$RESTART_NONCE to $DEPLOYMENT"
          echo "$PATCH" | kubectl patch flinkdeployment "$DEPLOYMENT" -n "$NAMESPACE" --type=json --patch-file=/dev/stdin
          
          if [ $? -eq 0 ]; then
            log "Successfully patched $DEPLOYMENT with STORAGE_SCHEMA_ID and triggered restart"
            ((SUCCESSFUL_DEPLOYMENTS++))
          else
            log "Failed to patch $DEPLOYMENT"
            ((FAILED_DEPLOYMENTS++))
          fi
        else
          log "[DRY-RUN] Would apply combined patch to add STORAGE_SCHEMA_ID=$SCHEMA_ID and restart $DEPLOYMENT with nonce=$RESTART_NONCE"
        fi
      else
        log "Error: Could not extract schemaId from ENRICHMENT_CONFIG in $DEPLOYMENT"
        ((FAILED_DEPLOYMENTS++))
      fi
    else
      log "Error: ENRICHMENT_CONFIG not found in $DEPLOYMENT"
      ((FAILED_DEPLOYMENTS++))
    fi
  else
    log "STORAGE_SCHEMA_ID already exists in $DEPLOYMENT, skipping"
    ((SKIPPED_DEPLOYMENTS++))
  fi
  
  log "-----------------------------------"
done

# Summary
log "Operation completed"
log "Summary:"
log "- Total deployments processed: $((SUCCESSFUL_DEPLOYMENTS + FAILED_DEPLOYMENTS + SKIPPED_DEPLOYMENTS))"
log "- Successfully patched and restarted: $SUCCESSFUL_DEPLOYMENTS"
log "- Failed to patch: $FAILED_DEPLOYMENTS"
log "- Skipped (already had STORAGE_SCHEMA_ID or user skipped): $SKIPPED_DEPLOYMENTS"
log "Log file: $LOG_FILE"

if [ "$DRY_RUN" = true ]; then
  log "This was a dry run. No changes were applied."
fi

echo "Done!"
