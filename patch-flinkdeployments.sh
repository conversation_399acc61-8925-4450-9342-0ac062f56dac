#!/bin/bash
# Script to apply specific configuration changes to multiple FlinkDeployment resources
# Patches TaskManager CPU, upgradeMode, DB_URL, and restartNonce values

# Default values
NAMESPACE=""
DEPLOYMENTS=()
DEPLOYMENTS_FILE=""
CPU_VALUE="4"
DB_URL=""
DRY_RUN=false
LOG_FILE="flinkdeployment-patch-$(date +%Y%m%d-%H%M%S).log"

# Function to display usage information
usage() {
  echo "Usage: $0 [OPTIONS] [DEPLOYMENT_NAMES...]"
  echo "Options:"
  echo "  -n, --namespace NAMESPACE    Specify the Kubernetes namespace (required)"
  echo "  -f, --file FILE              Read deployment names from file (one per line)"
  echo "  -c, --cpu VALUE              Set TaskManager CPU value (default: $CPU_VALUE)"
  echo "  -u, --db-url URL             Set custom PostgreSQL database URL"
  echo "                               (format: *******************************)"
  echo "  -d, --dry-run                Show what would be changed without applying"
  echo "  -h, --help                   Display this help message"
  echo ""
  echo "You can specify deployment names directly as arguments or use the -f option to read from a file."
  exit 1
}

# Function for logging
log() {
  local message="$1"
  local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] $message"
  echo "[$timestamp] $message" >> "$LOG_FILE"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    -n|--namespace)
      NAMESPACE="$2"
      shift 2
      ;;
    -f|--file)
      DEPLOYMENTS_FILE="$2"
      shift 2
      ;;
    -c|--cpu)
      CPU_VALUE="$2"
      shift 2
      ;;
    -u|--db-url)
      DB_URL="$2"
      shift 2
      ;;
    -d|--dry-run)
      DRY_RUN=true
      shift
      ;;
    -h|--help)
      usage
      ;;
    -*)
      echo "Unknown option: $1"
      usage
      ;;
    *)
      DEPLOYMENTS+=("$1")
      shift
      ;;
  esac
done

# Validate required parameters
if [ -z "$NAMESPACE" ]; then
  echo "Error: Namespace is required"
  usage
fi

# Load deployments from file if specified
if [ -n "$DEPLOYMENTS_FILE" ]; then
  if [ ! -f "$DEPLOYMENTS_FILE" ]; then
    echo "Error: Deployments file not found: $DEPLOYMENTS_FILE"
    exit 1
  fi
  
  while IFS= read -r line || [ -n "$line" ]; do
    # Skip empty lines and comments
    if [ -n "$line" ] && [[ ! "$line" =~ ^[[:space:]]*# ]]; then
      DEPLOYMENTS+=("$line")
    fi
  done < "$DEPLOYMENTS_FILE"
fi

# Check if we have any deployments to process
if [ ${#DEPLOYMENTS[@]} -eq 0 ]; then
  echo "Error: No deployments specified. Use command line arguments or -f option."
  usage
fi

# Check if required tools are available
if ! command -v kubectl &> /dev/null; then
  echo "Error: kubectl is not installed or not in PATH"
  exit 1
fi

if ! command -v jq &> /dev/null; then
  echo "Error: jq is not installed or not in PATH"
  exit 1
fi

log "Starting script to patch FlinkDeployments in namespace '$NAMESPACE'"
if [ "$DRY_RUN" = true ]; then
  log "Running in DRY-RUN mode - no changes will be applied"
fi
log "TaskManager CPU value to be set: $CPU_VALUE"
if [ -n "$DB_URL" ]; then
  log "Database URL to be set: $DB_URL"
fi
log "Deployments to be patched: ${DEPLOYMENTS[*]}"

# Validate that all specified deployments exist
INVALID_DEPLOYMENTS=()
for DEPLOYMENT in "${DEPLOYMENTS[@]}"; do
  if ! kubectl get flinkdeployment "$DEPLOYMENT" -n "$NAMESPACE" &>/dev/null; then
    log "Warning: FlinkDeployment '$DEPLOYMENT' not found in namespace '$NAMESPACE'"
    INVALID_DEPLOYMENTS+=("$DEPLOYMENT")
  fi
done

if [ ${#INVALID_DEPLOYMENTS[@]} -gt 0 ]; then
  log "The following deployments were not found: ${INVALID_DEPLOYMENTS[*]}"
  read -p "Do you want to continue with the valid deployments? (y/n): " CONTINUE
  if [[ ! "$CONTINUE" =~ ^[Yy]$ ]]; then
    log "Operation cancelled by user"
    exit 0
  fi
  
  # Remove invalid deployments from the list
  for INVALID in "${INVALID_DEPLOYMENTS[@]}"; do
    DEPLOYMENTS=(${DEPLOYMENTS[@]/$INVALID/})
  done
  
  # Clean up empty elements
  DEPLOYMENTS_CLEAN=()
  for DEPLOYMENT in "${DEPLOYMENTS[@]}"; do
    if [ -n "$DEPLOYMENT" ]; then
      DEPLOYMENTS_CLEAN+=("$DEPLOYMENT")
    fi
  done
  DEPLOYMENTS=("${DEPLOYMENTS_CLEAN[@]}")
  
  if [ ${#DEPLOYMENTS[@]} -eq 0 ]; then
    log "No valid deployments remaining. Exiting."
    exit 1
  fi
  
  log "Continuing with these valid deployments: ${DEPLOYMENTS[*]}"
fi

# Ask for confirmation before proceeding
if [ "$DRY_RUN" = false ]; then
  echo ""
  echo "The following changes will be applied to each deployment:"
  echo "1. TaskManager CPU will be set to: $CPU_VALUE"
  echo "2. Job upgradeMode will be set to: stateless"
  if [ -n "$DB_URL" ]; then
    echo "3. Database URL will be set to: $DB_URL"
    echo "4. A new restartNonce will be generated (causing a restart)"
  else
    echo "3. A new restartNonce will be generated (causing a restart)"
  fi
  echo ""
  read -p "Do you want to proceed with patching these deployments? (y/n): " CONFIRM
  if [[ ! "$CONFIRM" =~ ^[Yy]$ ]]; then
    log "Operation cancelled by user"
    exit 0
  fi
fi

SUCCESSFUL_PATCHES=0
FAILED_PATCHES=0

for DEPLOYMENT in "${DEPLOYMENTS[@]}"; do
  log "Processing $DEPLOYMENT..."
  
  # Generate a restartNonce value (timestamp-based)
  RESTART_NONCE=$(date +%s)
  
  # Start building the JSON patch
  PATCH_OPERATIONS="[
  {
    \"op\": \"replace\",
    \"path\": \"/spec/taskManager/resource/cpu\",
    \"value\": $CPU_VALUE
  },
  {
    \"op\": \"replace\",
    \"path\": \"/spec/job/upgradeMode\",
    \"value\": \"stateless\"
  }"
  
  # Add DB_URL patch if specified
  if [ -n "$DB_URL" ]; then
    # Get the container index that has the env vars
    CONTAINER_INDEX=$(kubectl get flinkdeployment "$DEPLOYMENT" -n "$NAMESPACE" -o json |  
      jq '.spec.podTemplate.spec.containers | map(.name == "flink-main-container") | index(true) // 0')
    
    # Find the index of the existing DB_URL environment variable
    DB_URL_INDEX=$(kubectl get flinkdeployment "$DEPLOYMENT" -n "$NAMESPACE" -o json | 
      jq ".spec.podTemplate.spec.containers[$CONTAINER_INDEX].env | map(.name == \"DB_URL\") | index(true)")
    
    if [ -z "$DB_URL_INDEX" ] || [ "$DB_URL_INDEX" == "null" ]; then
      log "Warning: DB_URL environment variable not found in $DEPLOYMENT, skipping DB_URL update"
    else
      log "Found DB_URL at index: $DB_URL_INDEX"
      
      # Add DB_URL patch operation
      PATCH_OPERATIONS="$PATCH_OPERATIONS,
  {
    \"op\": \"replace\",
    \"path\": \"/spec/podTemplate/spec/containers/$CONTAINER_INDEX/env/$DB_URL_INDEX\",
    \"value\": {
      \"name\": \"DB_URL\",
      \"value\": \"$DB_URL\"
    }
  }"
    fi
  fi
  
  # Add restartNonce patch operation and close the array
  PATCH_OPERATIONS="$PATCH_OPERATIONS,
  {
    \"op\": \"replace\",
    \"path\": \"/spec/restartNonce\",
    \"value\": $RESTART_NONCE
  }
]"
  
  # Create the final JSON patch
  PATCH="$PATCH_OPERATIONS"
  
  # Display the patch
  log "Patch to be applied to $DEPLOYMENT:"
  echo "$PATCH"
  
  if [ "$DRY_RUN" = false ]; then
    # Apply the JSON patch
    log "Applying patch to $DEPLOYMENT..."
    
    echo "$PATCH" | kubectl patch flinkdeployment "$DEPLOYMENT" -n "$NAMESPACE" --type=json --patch-file=/dev/stdin
    
    if [ $? -eq 0 ]; then
      log "Successfully patched $DEPLOYMENT"
      ((SUCCESSFUL_PATCHES++))
    else
      log "Failed to patch $DEPLOYMENT"
      ((FAILED_PATCHES++))
    fi
  else
    log "[DRY-RUN] Would patch $DEPLOYMENT with the above changes"
    ((SUCCESSFUL_PATCHES++))
  fi
  
  echo ""
done

# Summary
log "Patch operation completed"
log "Summary:"
log "- Total deployments processed: $((SUCCESSFUL_PATCHES + FAILED_PATCHES))"
log "- Successfully patched: $SUCCESSFUL_PATCHES"
log "- Failed to patch: $FAILED_PATCHES"
log "Log file: $LOG_FILE"

if [ "$DRY_RUN" = true ]; then
  log "This was a dry run. No changes were applied."
fi

echo "Done!"
