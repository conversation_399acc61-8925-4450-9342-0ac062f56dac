[2025-06-20 19:20:25] 🌐 Port-forwarding Flink JobManager REST API...
[2025-06-20 19:20:28] 🔍 Fetching running job ID...
[2025-06-20 19:20:29] ✅ Found running job: 8e8f9aade58f1486e1fd4976882083be
[2025-06-20 19:20:29] 🛑 Stopping job with drain=true and savepoint path: abfss://<EMAIL>/gdc-02716c8f-f3d0-452f-9401-2ff86858968a/savepoints//savepoint-1750440025
[2025-06-20 19:20:29] 🚦 Stop triggered, trigger ID: 4fb51643b91177b059453104b747ae3f.
[2025-06-20 19:20:29] ⏳ Waiting for job to stop...
[2025-06-20 19:20:34] ✅ Job stopped with state: FINISHED
[2025-06-20 19:20:34] ✅ Savepoint created at: abfss://<EMAIL>/gdc-02716c8f-f3d0-452f-9401-2ff86858968a/savepoints/savepoint-8e8f9a-3d085f7a3e06
[2025-06-20 19:20:34] 📦 Patching FlinkDeployment with savepoint, savepointRedeployNonce: 1750440034 and initialSavepointPath: abfss://<EMAIL>/gdc-02716c8f-f3d0-452f-9401-2ff86858968a/savepoints/savepoint-8e8f9a-3d085f7a3e06
[2025-06-20 19:20:35] 🚀 Restart requested from savepoint: abfss://<EMAIL>/gdc-02716c8f-f3d0-452f-9401-2ff86858968a/savepoints//savepoint-1750440025 (savepointRedeployNonce: 1750440034) and initialSavepointPath: abfss://<EMAIL>/gdc-02716c8f-f3d0-452f-9401-2ff86858968a/savepoints/savepoint-8e8f9a-3d085f7a3e06
[2025-06-20 19:20:35] ⏳ Verifying FlinkDeployment restart...
[2025-06-20 19:20:36] ⏳ Current job state: FINISHED (Timeout in 299s)
[2025-06-20 19:20:47] ⏳ Current job state: RECONCILING (Timeout in 288s)
[2025-06-20 19:20:58] ⏳ Current job state: RECONCILING (Timeout in 277s)
[2025-06-20 19:21:09] ⏳ Current job state: RECONCILING (Timeout in 266s)
[2025-06-20 19:21:20] ✅ FlinkDeployment successfully restarted and is now RUNNING
[2025-06-20 19:21:20] === Restart Operation Summary ===
[2025-06-20 19:21:20] FlinkDeployment: gdc-02716c8f-f3d0-452f-9401-2ff86858968a
[2025-06-20 19:21:20] Namespace: str-int
[2025-06-20 19:21:20] Savepoint path: abfss://<EMAIL>/gdc-02716c8f-f3d0-452f-9401-2ff86858968a/savepoints/savepoint-8e8f9a-3d085f7a3e06
[2025-06-20 19:21:20] Final state: RUNNING
[2025-06-20 19:21:20] ✅ Restart operation completed successfully
[2025-06-20 19:21:20] 🧹 Cleaning up port-forward
